<p>login works!</p>
<form (ngSubmit)="onSubmit()" #loginForm="ngForm">
  <div>
    <label for="username">Username</label>
    <input 
      id="username" 
      [(ngModel)]="username" 
      name="username" 
      required 
      #usernameInput="ngModel" 
      placeholder="Enter your username" 
      type="text" 
    />
    <div *ngIf="usernameInput.invalid && usernameInput.touched" class="error-message">
      Username is required.
    </div>
  </div>

  <div>
    <label for="password">Password</label>
    <input 
      id="password" 
      [(ngModel)]="password" 
      name="password" 
      type="password" 
      required 
      #passwordInput="ngModel" 
      placeholder="Enter your password" 
    />
    <div *ngIf="passwordInput.invalid && passwordInput.touched" class="error-message">
      Password is required.
    </div>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <button type="submit" [disabled]="loginForm.invalid || isLoading">
    <span *ngIf="isLoading">Logging in...</span>
    <span *ngIf="!isLoading">Login</span>
  </button>
</form>
