export interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  posts: CalendarPost[];
  projects: CalendarProject[];
}

export interface CalendarPost {
  id: number;
  title: string;
  description: string;
  time: string;
  status: PostStatus;
  mediaType: 'image' | 'video' | 'file' | null;
  thumbnailUrl?: string;
  scheduledDate: Date;
  projectTitle: string;
  companyName: string;
  projectId: number;
  companyId: number;
  deadline?: string;
}

export interface CalendarProject {
  id: number;
  title: string;
  name: string;
  companyName: string;
  companyId: number;
  deadline?: string;
  postsCount: number;
  pendingPostsCount: number;
  company: {
    id: number;
    name: string;
    logo?: string;
  };
}

export interface CalendarEvent {
  id: string;
  type: 'post' | 'project';
  title: string;
  description?: string;
  projectTitle?: string;
  project_title?: string; // Backend compatibility
  companyName: string;
  company_name?: string; // Backend compatibility
  date: string;
  deadline?: string;
  status?: PostStatus;
  projectId: number;
  project_id?: number; // Backend compatibility
  companyId: number;
  company_id?: number; // Backend compatibility
  postId?: number;
  post_id?: number; // Backend compatibility
  thumbnailUrl?: string;
  thumbnail_url?: string; // Backend compatibility
  mediaType?: 'image' | 'video' | 'file';
  media_type?: 'image' | 'video' | 'file'; // Backend compatibility
}

export type PostStatus = 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled';

export interface CalendarFilter {
  statuses: PostStatus[];
  searchTerm: string;
  tags: string[];
  projectIds: number[];
  companyIds: number[];
}

export interface StatusConfig {
  color: string;
  icon: string;
  label: string;
}

export interface CalendarViewState {
  currentDate: Date;
  selectedDate: Date | null;
  filter: CalendarFilter;
  viewMode: 'month' | 'week';
}

export interface CreatorDashboardData {
  events: CalendarEvent[];
  projects: CalendarProject[];
}
