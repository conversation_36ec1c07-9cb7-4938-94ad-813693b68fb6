import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule, MatDatepicker } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar } from '@angular/material/snack-bar';

import { SuperAdminService } from '../../services/super-admin.service';
import { ProjectCreationRequest, User, Project } from '../../models/super-admin.models';

export interface ProjectDialogData {
  companyId: number;
  companyName: string;
  project?: Project; // If provided, we're editing; if not, we're creating
  availableCreators?: User[];
  availableAdmins?: User[];
}

@Component({
  selector: 'app-project-management-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule
  ],
  template: `
    <h2 mat-dialog-title>
      <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
      {{ isEditMode ? 'Edit Project - ' + data.project?.title : 'Create New Project' }}
    </h2>

    <mat-dialog-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>{{ isEditMode ? 'Loading project details...' : 'Loading...' }}</p>
      </div>

      <form [formGroup]="projectForm" class="project-form" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Project Name *</mat-label>
            <input matInput formControlName="name" placeholder="Enter project name">
            <mat-error *ngIf="projectForm.get('name')?.hasError('required')">
              Project name is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Project Title *</mat-label>
            <input matInput formControlName="title" placeholder="Enter project title">
            <mat-error *ngIf="projectForm.get('title')?.hasError('required')">
              Project title is required
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Description</mat-label>
          <textarea matInput formControlName="description" 
                    placeholder="Enter project description" 
                    rows="3"></textarea>
        </mat-form-field>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Deadline *</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="deadline"
                   [min]="minDate" placeholder="Select deadline" readonly
                   (click)="openDatePicker()">
            <mat-datepicker-toggle matSuffix [for]="picker" (click)="openDatePicker()"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-hint>Deadline is mandatory</mat-hint>
            <mat-error *ngIf="projectForm.get('deadline')?.hasError('required')">
              Please select a valid deadline
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width" *ngIf="availableAdmins.length > 0">
            <mat-label>Assign Company Admin</mat-label>
            <mat-select formControlName="company_admin_id">
              <mat-option value="">Select Admin</mat-option>
              <mat-option *ngFor="let admin of availableAdmins" [value]="admin.id">
                {{ admin.username }} - {{ admin.email }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width" *ngIf="availableCreators.length > 0">
          <mat-label>Assign Creators</mat-label>
          <mat-select formControlName="creator_ids" multiple>
            <mat-option *ngFor="let creator of availableCreators" [value]="creator.id">
              {{ creator.username }} - {{ creator.email }}
              <span *ngIf="creator.company" class="user-company-info"> ({{ creator.company.name }})</span>
            </mat-option>
          </mat-select>
          <mat-hint>{{ availableCreators.length }} creator(s) available</mat-hint>
        </mat-form-field>

        <div class="company-info">
          <mat-icon>business</mat-icon>
          <span>Company: <strong>{{ data.companyName }}</strong></span>
        </div>
      </form>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button 
              color="primary" 
              (click)="onSave()"
              [disabled]="projectForm.invalid || isSaving">
        <mat-icon>{{ isEditMode ? 'save' : 'add' }}</mat-icon>
        {{ isSaving ? 'Saving...' : (isEditMode ? 'Update Project' : 'Create Project') }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .project-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-width: 500px;
      max-width: 700px;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .half-width {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      text-align: center;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    .company-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-top: 8px;
    }

    .company-info mat-icon {
      color: #1976d2;
    }

    .user-company-info {
      font-size: 12px;
      color: #666;
      font-style: italic;
    }

    mat-dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    /* Make date picker more clickable */
    .mat-mdc-form-field input[readonly] {
      cursor: pointer;
    }

    .mat-datepicker-toggle {
      cursor: pointer;
    }

    /* Ensure date picker field is fully clickable */
    .mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay {
      opacity: 0;
    }
  `]
})
export class ProjectManagementDialogComponent implements OnInit {
  @ViewChild('picker') datePicker!: MatDatepicker<Date>;

  projectForm: FormGroup;
  isLoading = false;
  isSaving = false;
  isEditMode = false;
  minDate = new Date(); // Prevent past dates

  availableCreators: User[] = [];
  availableAdmins: User[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<ProjectManagementDialogComponent>,
    private superAdminService: SuperAdminService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: ProjectDialogData
  ) {
    this.isEditMode = !!data.project;
    this.availableCreators = data.availableCreators || [];
    this.availableAdmins = data.availableAdmins || [];

    this.projectForm = this.fb.group({
      name: ['', [Validators.required]],
      title: ['', [Validators.required]],
      description: [''],
      deadline: ['', [Validators.required]], // Make deadline required
      company_admin_id: [''],
      creator_ids: [[]]
    });
  }

  ngOnInit(): void {
    if (this.isEditMode && this.data.project) {
      this.loadProjectData();
    }
  }

  loadProjectData(): void {
    const project = this.data.project!;

    // Handle deadline conversion
    let deadlineValue = null;
    if (project.deadline) {
      try {
        deadlineValue = new Date(project.deadline);
      } catch (error) {
        console.error('Error parsing project deadline:', error);
        deadlineValue = null;
      }
    }

    this.projectForm.patchValue({
      name: project.name,
      title: project.title,
      description: project.description || '',
      deadline: deadlineValue,
      creator_ids: project.creators?.map(c => c.id) || []
    });

    console.log('Loaded project data:', project);
    console.log('Form values after loading:', this.projectForm.value);
  }

  openDatePicker(): void {
    if (this.datePicker) {
      this.datePicker.open();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.projectForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isSaving = true;
    const formValue = this.projectForm.value;

    // Handle deadline formatting
    let deadlineString: string | undefined = undefined;
    if (formValue.deadline) {
      try {
        const deadlineDate = new Date(formValue.deadline);
        if (isNaN(deadlineDate.getTime())) {
          throw new Error('Invalid date');
        }
        // Format as YYYY-MM-DD for backend compatibility
        deadlineString = deadlineDate.toISOString().split('T')[0];
        console.log('Formatted deadline:', deadlineString);
      } catch (error) {
        console.error('Error formatting deadline:', error);
        this.snackBar.open('Invalid deadline format', 'Close', { duration: 3000 });
        this.isSaving = false;
        return;
      }
    }

    const projectData: ProjectCreationRequest = {
      name: formValue.name,
      title: formValue.title,
      description: formValue.description || '',
      deadline: deadlineString,
      company_id: this.data.companyId,
      creator_ids: formValue.creator_ids || [],
      company_admin_id: formValue.company_admin_id || undefined
    };

    console.log('Saving project data:', projectData);

    const operation = this.isEditMode
      ? this.superAdminService.updateProject(this.data.project!.id, projectData)
      : this.superAdminService.createProject(projectData);

    operation.subscribe({
      next: (response) => {
        console.log('Project save response:', response);
        const message = this.isEditMode ? 'Project updated successfully!' : 'Project created successfully!';
        this.snackBar.open(message, 'Close', { duration: 3000 });
        this.dialogRef.close(true); // Return true to indicate success
      },
      error: (error) => {
        console.error('Error saving project:', error);
        console.error('Error details:', error.error);
        let errorMessage = this.isEditMode ? 'Failed to update project' : 'Failed to create project';

        // Show more specific error message if available
        if (error.error && error.error.error) {
          errorMessage += ': ' + error.error.error;
        }

        this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        this.isSaving = false;
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.projectForm.controls).forEach(key => {
      this.projectForm.get(key)?.markAsTouched();
    });
  }
}
