<h2 mat-dialog-title>Create Post for {{ data.date }}</h2>

<mat-dialog-content>
  <form [formGroup]="postForm" (ngSubmit)="onSubmit()" class="post-form">

    <!-- Project Selection -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Project *</mat-label>
      <mat-select formControlName="project">
        <mat-option *ngFor="let project of assignedProjects" [value]="project.id">
          {{ project.name }} - {{ project.company_name }}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="postForm.get('project')?.hasError('required')">
        Please select a project
      </mat-error>
    </mat-form-field>

    <!-- Title -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Title *</mat-label>
      <input matInput formControlName="title" placeholder="Enter post title">
      <mat-error *ngIf="postForm.get('title')?.hasError('required')">
        Title is required
      </mat-error>
      <mat-error *ngIf="postForm.get('title')?.hasError('minlength')">
        Title must be at least 3 characters long
      </mat-error>
    </mat-form-field>

    <!-- Description -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Description *</mat-label>
      <textarea matInput formControlName="description"
                placeholder="Enter post description"
                rows="4"></textarea>
      <mat-error *ngIf="postForm.get('description')?.hasError('required')">
        Description is required
      </mat-error>
      <mat-error *ngIf="postForm.get('description')?.hasError('minlength')">
        Description must be at least 10 characters long
      </mat-error>
    </mat-form-field>

    <!-- Scheduled Date and Time -->
    <div class="datetime-row">
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Scheduled Date *</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="scheduled_date" readonly>
        <mat-datepicker-toggle matSuffix [for]="picker">
          <mat-icon matDatepickerToggleIcon>calendar_today</mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="postForm.get('scheduled_date')?.hasError('required')">
          Scheduled date is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Scheduled Time *</mat-label>
        <input matInput type="time" formControlName="scheduled_time" placeholder="HH:MM">
        <mat-icon matSuffix>access_time</mat-icon>
        <mat-error *ngIf="postForm.get('scheduled_time')?.hasError('required')">
          Scheduled time is required
        </mat-error>
      </mat-form-field>
    </div>

    <!-- File Upload -->
    <div class="file-upload-section">
      <label class="file-upload-label">Media File *</label>
      <input type="file"
             (change)="onFileChange($event)"
             accept="image/*,video/*"
             class="file-input" />
      <div class="file-upload-hint">
        Supported formats: JPG, PNG, GIF, MP4, WebM (Max 10MB)
      </div>
    </div>

    <!-- Preview -->
    <div *ngIf="previewUrl" class="preview-section">
      <h4>Preview:</h4>
      <img *ngIf="file && file.type && file.type.startsWith('image/')"
           [src]="previewUrl"
           class="preview-image" />
      <video *ngIf="file && file.type && file.type.startsWith('video/')"
             [src]="previewUrl"
             class="preview-video"
             controls></video>
    </div>

  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">
    Cancel
  </button>
  <button mat-raised-button
          color="primary"
          (click)="onSubmit()"
          [disabled]="postForm.invalid || !file || isSubmitting">
    <span *ngIf="isSubmitting">Creating...</span>
    <span *ngIf="!isSubmitting">Create Post</span>
  </button>
</mat-dialog-actions>
