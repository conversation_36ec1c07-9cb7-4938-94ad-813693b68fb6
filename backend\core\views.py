from django.shortcuts import render
from rest_framework import viewsets, permissions
from .models import User, Company, Project, Post
from .serializers import UserSerializer, CompanySerializer, ProjectSerializer, PostSerializer
from django.http import JsonResponse
from django.contrib.auth import authenticate
from rest_framework.decorators import api_view,permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import CustomTokenObtainPairSerializer
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import exception_handler
from .serializers import UserSerializer
from django.db import models
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import PermissionDenied
from rest_framework_simplejwt.authentication import JWTAuthentication



# ViewSets
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer


class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.all()
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter projects based on user role"""
        user = self.request.user
        if user.role == 'super_admin':
            return Project.objects.all()
        elif user.role == 'company_admin':
            return Project.objects.filter(company__admins=user)
        elif user.role == 'creator':
            return Project.objects.filter(creators=user)
        return Project.objects.none()

    def perform_create(self, serializer):
        """Only super_admin and company_admin can create projects"""
        user = self.request.user
        if user.role not in ['super_admin', 'company_admin']:
            raise PermissionDenied("You don't have permission to create projects.")
        serializer.save()


class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all()
    serializer_class = PostSerializer

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        """Override to add better logging and error handling"""
        try:
            response = super().post(request, *args, **kwargs)
            if response.status_code == 200:
                # Log successful authentication
                username = request.data.get('username', 'unknown')
                print(f"✅ Successful token generation for user: {username}")
            return response
        except Exception as e:
            # Log authentication failures with more detail
            username = request.data.get('username', 'unknown')
            print(f"❌ Token generation failed for user: {username}, Error: {str(e)}")
            return Response(
                {'detail': 'Authentication failed. Please check your credentials.'},
                status=401
            )

# JWT login endpoint
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_current_user(request):
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'role': user.role,
        'company': {
            'id': user.company.id,
            'name': user.company.name
        } if user.company else None,
        'is_active': user.is_active,
        'date_joined': user.date_joined.isoformat()
    })

@api_view(['POST'])
def login_view(request):
    username = request.data.get('username')
    password = request.data.get('password')
    user = authenticate(username=username, password=password)

    if user:
        refresh = RefreshToken.for_user(user)
        return JsonResponse({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'company': {
                    'id': user.company.id,
                    'name': user.company.name
                } if user.company else None,
                'is_active': user.is_active,
                'date_joined': user.date_joined.isoformat()
            }
        })
    return JsonResponse({'error': 'Invalid credentials'}, status=401)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_posts(request):
    user = request.user
    if user.role == 'creator':
        posts = Post.objects.filter(creator=user)
    elif user.role == 'company_admin':
        posts = Post.objects.filter(project__company=user.company)
    else:
        posts = Post.objects.all()
    
    serializer = PostSerializer(posts, many=True, context={'request': request})
    return Response(serializer.data)

class SuperAdminViewSet(viewsets.ViewSet):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def _check_super_admin_permission(self, request):
        """Check if user is super admin"""
        if request.user.role != 'super_admin':
            return Response({'error': 'Access denied. Super admin role required.'}, status=403)
        return None

    def _check_permission_matrix(self, request, action):
        """Enforce permission matrix rules"""
        user_role = request.user.role

        # Permission matrix implementation
        permissions = {
            'create_company': ['super_admin'],
            'create_project': ['super_admin', 'company_admin'],
            'set_project_title_deadline': ['super_admin', 'company_admin'],
            'review_post': ['company_admin'],
            'post_creation_submit': ['creator'],
            'assign_creator': ['super_admin', 'company_admin']
        }

        if action in permissions and user_role not in permissions[action]:
            return Response({
                'error': f'Access denied. {action} requires one of: {", ".join(permissions[action])}'
            }, status=403)
        return None

    # List companies with detailed admin and creator information
    @action(detail=False, methods=['get'])
    def list_companies(self, request):
        """Get all companies with detailed admin and creator information"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        companies = Company.objects.all().prefetch_related('users', 'projects')
        companies_data = []

        for company in companies:
            # Get admins and creators for this company
            admins = company.users.filter(role='company_admin')
            creators = company.users.filter(role='creator')
            projects = company.projects.all()

            companies_data.append({
                'id': company.id,
                'name': company.name,
                'logo': company.logo.url if company.logo else None,
                'is_active': True,  # Default to active for now
                'admin_count': admins.count(),
                'creator_count': creators.count(),
                'project_count': projects.count(),
                'admins': [
                    {
                        'id': admin.id,
                        'username': admin.username,
                        'first_name': admin.first_name,
                        'last_name': admin.last_name,
                        'email': admin.email,
                        'full_name': f"{admin.first_name} {admin.last_name}".strip() or admin.username,
                        'date_joined': admin.date_joined.isoformat()
                    } for admin in admins
                ],
                'creators': [
                    {
                        'id': creator.id,
                        'username': creator.username,
                        'first_name': creator.first_name,
                        'last_name': creator.last_name,
                        'email': creator.email,
                        'full_name': f"{creator.first_name} {creator.last_name}".strip() or creator.username,
                        'assigned_projects_count': creator.assigned_projects.filter(company=company).count(),
                        'date_joined': creator.date_joined.isoformat()
                    } for creator in creators
                ],
                'projects': [
                    {
                        'id': project.id,
                        'name': project.name,
                        'title': project.title,
                        'deadline': project.deadline.isoformat() if project.deadline else None,
                        'creators_count': project.creators.count()
                    } for project in projects
                ],
                'status': 'active' if admins.count() > 0 or creators.count() > 0 else 'inactive'
            })

        return Response(companies_data)
    
    # Add a new company
    @action(detail=False, methods=['post'])
    def add_company(self, request):
        # Check permission matrix
        permission_error = self._check_permission_matrix(request, 'create_company')
        if permission_error:
            return permission_error

        serializer = CompanySerializer(data=request.data)
        if serializer.is_valid():
            company = serializer.save()
            return Response({"message": "Company created successfully", "company": serializer.data})
        return Response(serializer.errors, status=400)
    
    # Add a project to a company
    @action(detail=True, methods=['post'])
    def add_project(self, request, pk=None):
        # Check permission matrix
        permission_error = self._check_permission_matrix(request, 'create_project')
        if permission_error:
            return permission_error

        try:
            company = Company.objects.get(id=pk)
        except Company.DoesNotExist:
            return Response({"error": "Company not found."}, status=404)

        project_data = request.data
        project_data['company'] = company.id
        serializer = ProjectSerializer(data=project_data)

        if serializer.is_valid():
            serializer.save()

            # Fetch and return updated project list
            projects = Project.objects.filter(company=company)
            project_list_serializer = ProjectSerializer(projects, many=True)

            return Response({
                "message": "Project added successfully",
                "projects": project_list_serializer.data
            })
        return Response(serializer.errors, status=400)

    # Assign a content creator to a project
    @action(detail=True, methods=['post'], url_path='assign_creator')
    def assign_creator(self, request, pk=None):
        # Check permission matrix
        permission_error = self._check_permission_matrix(request, 'assign_creator')
        if permission_error:
            return permission_error

        try:
            project_id = pk
            creator_id = request.data.get('creator_id')

            project = Project.objects.get(id=project_id)
            creator = User.objects.get(id=creator_id)
            project.creators.add(creator)

            return Response({"message": "Creator assigned successfully."}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    # ✅ List projects under a company
    @action(detail=False, methods=['get'], url_path='list_projects/(?P<company_id>[^/.]+)')
    def list_projects(self, request, company_id=None):
        try:
            company = Company.objects.get(id=company_id)
            projects = Project.objects.filter(company=company).prefetch_related('creators')

            project_data = []
            for project in projects:
                creators = project.creators.all()
                creators_data = [{'id': c.id, 'username': c.username} for c in creators]
                project_data.append({
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': company.id,
                    'creators': creators_data,
                    'created_at': project.created_at.isoformat() if project.created_at else None
                })

            return Response(project_data)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)

    # ✅ Get dashboard statistics for Super Admin
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get comprehensive dashboard statistics for Super Admin"""
        # Company statistics
        total_companies = Company.objects.count()
        active_companies = Company.objects.filter(users__isnull=False).distinct().count()

        # Admin statistics
        total_admins = User.objects.filter(role='company_admin').count()
        active_admins = User.objects.filter(role='company_admin', company__isnull=False).count()

        # Creator statistics
        total_creators = User.objects.filter(role='creator').count()
        assigned_creators = User.objects.filter(role='creator', assigned_projects__isnull=False).distinct().count()

        # Content statistics
        total_posts = Post.objects.count()
        pending_reviews = Post.objects.filter(status='submitted').count()

        return Response({
            'companies': {
                'total': total_companies,
                'active': active_companies
            },
            'admins': {
                'total': total_admins,
                'active': active_admins
            },
            'creators': {
                'total': total_creators,
                'assigned': assigned_creators
            },
            'content': {
                'total_posts': total_posts,
                'pending_reviews': pending_reviews
            }
        })

    # ✅ List all users by role with improved filtering
    @action(detail=False, methods=['get'])
    def list_users(self, request):
        """List users filtered by role with eligibility filtering for admin assignment"""
        role = request.query_params.get('role', None)
        eligible_for_admin = request.query_params.get('eligible_for_admin', 'false').lower() == 'true'

        if role:
            users = User.objects.filter(role=role)
        else:
            users = User.objects.all()

        # Filter users eligible for company admin assignment
        if eligible_for_admin:
            users = users.filter(
                models.Q(role='creator') |  # Creators can be promoted to admin
                models.Q(role='super_admin') |  # Super admins can be assigned as company admin
                models.Q(role='company_admin', company__isnull=True)  # Unassigned company admins
            ).exclude(
                role='company_admin', company__isnull=False  # Exclude already assigned company admins
            )

        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'company': {
                    'id': user.company.id,
                    'name': user.company.name
                } if user.company else None,
                'is_active': user.is_active,
                'date_joined': user.date_joined.isoformat(),
                'eligible_for_admin': user.role in ['creator', 'super_admin'] or (user.role == 'company_admin' and not user.company)
            })

        return Response(users_data)

    # ✅ Assign Company Admin to a company
    @action(detail=False, methods=['post'])
    def assign_admin(self, request):
        """Assign a user as Company Admin to a specific company"""
        user_id = request.data.get('user_id')
        company_id = request.data.get('company_id')

        try:
            user = User.objects.get(id=user_id)
            company = Company.objects.get(id=company_id)

            # Update user role and company
            user.role = 'company_admin'
            user.company = company
            user.save()

            return Response({
                'message': f'User {user.username} assigned as Company Admin to {company.name}',
                'user_id': user.id,
                'company_id': company.id
            })

        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    # ✅ Assign Content Creator to a company
    @action(detail=False, methods=['post'])
    def assign_creator_to_company(self, request):
        """Assign a creator to a company by linking them to company projects"""
        creator_id = request.data.get('creator_id')
        company_id = request.data.get('company_id')

        try:
            creator = User.objects.get(id=creator_id, role='creator')
            company = Company.objects.get(id=company_id)

            # Update creator's company association
            creator.company = company
            creator.save()

            return Response({
                'message': f'Creator {creator.username} assigned to {company.name}',
                'creator_id': creator.id,
                'company_id': company.id
            })

        except User.DoesNotExist:
            return Response({'error': 'Creator not found'}, status=404)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    # ✅ Get recent activity logs
    @action(detail=False, methods=['get'])
    def activity_logs(self, request):
        """Get recent activity logs for Super Admin dashboard"""
        # For now, we'll create mock activity data based on recent database changes
        # In a production system, you'd want a proper activity logging system

        activities = []

        # Recent company admins
        recent_admins = User.objects.filter(
            role='company_admin',
            company__isnull=False
        ).order_by('-date_joined')[:5]

        for admin in recent_admins:
            activities.append({
                'id': f'admin_{admin.id}',
                'type': 'admin_assignment',
                'message': f'Admin {admin.username} assigned to {admin.company.name}',
                'timestamp': admin.date_joined.isoformat(),
                'user': admin.username,
                'company': admin.company.name if admin.company else None
            })

        # Recent creator assignments
        recent_creators = User.objects.filter(
            role='creator',
            company__isnull=False
        ).order_by('-date_joined')[:5]

        for creator in recent_creators:
            activities.append({
                'id': f'creator_{creator.id}',
                'type': 'creator_assignment',
                'message': f'Creator {creator.username} added to {creator.company.name}',
                'timestamp': creator.date_joined.isoformat(),
                'user': creator.username,
                'company': creator.company.name if creator.company else None
            })

        # Recent companies
        recent_companies = Company.objects.order_by('-id')[:3]
        for company in recent_companies:
            # Use a proper timestamp - for new companies without created_at, use current time
            import datetime
            timestamp = datetime.datetime.now().isoformat()
            activities.append({
                'id': f'company_{company.id}',
                'type': 'company_creation',
                'message': f'Company {company.name} created',
                'timestamp': timestamp,
                'company': company.name
            })

        # Sort by timestamp (most recent first)
        activities.sort(key=lambda x: x['timestamp'], reverse=True)

        return Response(activities[:10])  # Return top 10 recent activities

    # ✅ Create Project with Title and Deadline
    @action(detail=False, methods=['post'])
    def create_project(self, request):
        """Create a new project with title, deadline, and assignments"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        permission_error = self._check_permission_matrix(request, 'create_project')
        if permission_error:
            return permission_error

        try:
            data = request.data

            # Required fields validation
            required_fields = ['name', 'title', 'company_id', 'deadline']
            for field in required_fields:
                if not data.get(field):
                    return Response({'error': f'{field} is required'}, status=400)

            # Validate company exists
            try:
                company = Company.objects.get(id=data['company_id'])
            except Company.DoesNotExist:
                return Response({'error': 'Company not found'}, status=404)

            # Handle deadline formatting
            deadline_value = data.get('deadline')
            parsed_deadline = None
            if deadline_value:
                if isinstance(deadline_value, str):
                    from datetime import datetime
                    try:
                        # Try parsing ISO format first
                        parsed_deadline = datetime.fromisoformat(deadline_value.replace('Z', '+00:00'))
                    except ValueError:
                        try:
                            # Try parsing other common formats
                            parsed_deadline = datetime.strptime(deadline_value, '%Y-%m-%d')
                        except ValueError:
                            print(f"Could not parse deadline: {deadline_value}")
                            parsed_deadline = None
                else:
                    parsed_deadline = deadline_value

            # Create project
            project = Project.objects.create(
                name=data['name'],
                title=data['title'],
                description=data.get('description', ''),
                deadline=parsed_deadline,
                company=company,
                created_by=request.user
            )

            # Assign creators if provided
            creator_ids = data.get('creator_ids', [])
            if creator_ids:
                creators = User.objects.filter(id__in=creator_ids, role='creator')
                project.creators.set(creators)

            # Assign company admin if provided
            company_admin_id = data.get('company_admin_id')
            if company_admin_id:
                try:
                    admin = User.objects.get(id=company_admin_id)
                    admin.role = 'company_admin'
                    admin.company = company
                    admin.save()
                except User.DoesNotExist:
                    pass  # Continue without error if admin not found

            return Response({
                'message': 'Project created successfully',
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': company.name,
                    'creators': [{'id': c.id, 'username': c.username} for c in project.creators.all()]
                }
            }, status=201)

        except Exception as e:
            return Response({'error': str(e)}, status=500)

    # ✅ Update Project
    @action(detail=False, methods=['put'], url_path='update_project/(?P<project_id>[^/.]+)')
    def update_project(self, request, project_id=None):
        """Update an existing project"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            print(f"Updating project {project_id} with data: {request.data}")
            project = Project.objects.get(id=project_id)
            data = request.data

            # Update project fields
            if 'name' in data:
                project.name = data['name']
            if 'title' in data:
                project.title = data['title']
            if 'description' in data:
                project.description = data['description']
            if 'deadline' in data:
                deadline_value = data['deadline']
                if deadline_value:
                    # Handle both string and date formats
                    if isinstance(deadline_value, str):
                        from datetime import datetime
                        try:
                            # Try parsing ISO format first
                            project.deadline = datetime.fromisoformat(deadline_value.replace('Z', '+00:00'))
                        except ValueError:
                            try:
                                # Try parsing other common formats
                                project.deadline = datetime.strptime(deadline_value, '%Y-%m-%d')
                            except ValueError:
                                print(f"Could not parse deadline: {deadline_value}")
                                project.deadline = None
                    else:
                        project.deadline = deadline_value
                else:
                    project.deadline = None

            project.save()
            print(f"Project saved successfully: {project.name}")

            # Update creators if provided
            if 'creator_ids' in data:
                project.creators.set(data['creator_ids'])
                print(f"Updated creators: {data['creator_ids']}")

            return Response({
                'message': 'Project updated successfully',
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': project.company.name,
                    'creators': [{'id': c.id, 'username': c.username} for c in project.creators.all()]
                }
            })

        except Project.DoesNotExist:
            print(f"Project {project_id} not found")
            return Response({'error': 'Project not found'}, status=404)
        except Exception as e:
            print(f"Error updating project: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({'error': str(e)}, status=500)

    # ✅ Get Company Settings and Projects
    @action(detail=True, methods=['get'])
    def company_settings(self, request, pk=None):
        """Get company settings including projects and assignments"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            company = Company.objects.get(id=pk)

            # Get company projects
            projects = Project.objects.filter(company=company).prefetch_related('creators')

            # Get company admin
            company_admin = User.objects.filter(company=company, role='company_admin').first()

            # Get available users for assignment
            available_users = User.objects.filter(
                models.Q(role='creator') |
                models.Q(role='company_admin') |
                models.Q(role__in=['super_admin'], company__isnull=True)
            )

            return Response({
                'company': {
                    'id': company.id,
                    'name': company.name
                },
                'admin': {
                    'id': company_admin.id,
                    'username': company_admin.username,
                    'email': company_admin.email
                } if company_admin else None,
                'projects': [
                    {
                        'id': p.id,
                        'name': p.name,
                        'title': p.title,
                        'description': p.description,
                        'deadline': p.deadline.isoformat() if p.deadline else None,
                        'creators': [{'id': c.id, 'username': c.username} for c in p.creators.all()],
                        'created_at': p.created_at.isoformat()
                    } for p in projects
                ],
                'available_users': [
                    {
                        'id': u.id,
                        'username': u.username,
                        'role': u.role,
                        'email': u.email,
                        'first_name': u.first_name,
                        'last_name': u.last_name,
                        'company': {
                            'id': u.company.id,
                            'name': u.company.name
                        } if u.company else None
                    } for u in available_users
                ]
            })

        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        
class CreatorDashboardViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def _check_creator_permission(self, request):
        """Check if user is creator"""
        if request.user.role != 'creator':
            return Response({'error': 'Access denied. Creator role required.'}, status=403)
        return None

    @action(detail=False, methods=['get'])
    def my_projects(self, request):
        """Get projects assigned to the creator with company details and deadlines"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        user = request.user
        projects = Project.objects.filter(creators=user).select_related('company', 'created_by')
        data = []
        for project in projects:
            # Get project deadline from latest post or set a default
            latest_post = Post.objects.filter(project=project).order_by('-scheduled_date').first()
            deadline = latest_post.scheduled_date if latest_post and latest_post.scheduled_date else None

            # Use project deadline if available, otherwise use latest post deadline
            if project.deadline:
                deadline = project.deadline

            # Get the company admin who assigned this project
            company_admin = project.created_by if project.created_by and project.created_by.role == 'company_admin' else None

            # If no specific admin assigned the project, get the first company admin for this company
            if not company_admin:
                company_admin = project.company.users.filter(role='company_admin').first()

            data.append({
                'id': project.id,
                'name': project.name,
                'title': project.title or project.name,  # Use project title if available
                'description': project.description,
                'company': {
                    'id': project.company.id,
                    'name': project.company.name,
                    'logo': project.company.logo.url if project.company.logo else None
                },
                'company_name': project.company.name,  # For easy access
                'company_admin': {
                    'id': company_admin.id,
                    'username': company_admin.username,
                    'first_name': company_admin.first_name,
                    'last_name': company_admin.last_name,
                    'email': company_admin.email,
                    'full_name': f"{company_admin.first_name} {company_admin.last_name}".strip() or company_admin.username
                } if company_admin else None,
                'deadline': deadline.isoformat() if deadline else None,
                'created_at': project.created_at.isoformat() if project.created_at else None,
                'posts_count': Post.objects.filter(project=project, creator=user).count(),
                'pending_posts_count': Post.objects.filter(
                    project=project,
                    creator=user,
                    status='submitted'
                ).count()
            })
        return Response(data)

    @action(detail=False, methods=['get'])
    def my_posts(self, request):
        """Get posts created by the creator, filtered by assigned projects only"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        # Get assigned projects
        assigned_projects = Project.objects.filter(creators=request.user)
        print(f"User {request.user.username} has {assigned_projects.count()} assigned projects")

        # Get posts from assigned projects (created by this user)
        posts = Post.objects.filter(
            creator=request.user,
            project__in=assigned_projects
        ).select_related('project', 'project__company')

        print(f"Found {posts.count()} posts for user {request.user.username}")

        serializer = PostSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def calendar_data(self, request):
        """Get calendar data with project assignments and thumbnails"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        user = request.user
        # Get assigned projects
        projects = Project.objects.filter(creators=user).select_related('company')

        # Get posts for assigned projects only
        posts = Post.objects.filter(
            creator=user,
            project__in=projects
        ).select_related('project', 'project__company')

        calendar_events = []

        # Add project events (deadlines)
        for project in projects:
            latest_post = Post.objects.filter(project=project).order_by('-scheduled_date').first()
            if latest_post and latest_post.scheduled_date:
                calendar_events.append({
                    'id': f'project_{project.id}',
                    'type': 'project',
                    'title': project.name,
                    'company_name': project.company.name,
                    'date': latest_post.scheduled_date.isoformat(),
                    'deadline': latest_post.scheduled_date.isoformat(),
                    'project_id': project.id,
                    'company_id': project.company.id
                })

        # Add post events with thumbnails
        for post in posts:
            event_data = {
                'id': f'post_{post.id}',
                'type': 'post',
                'title': post.title,
                'description': post.description,
                'project_title': post.project.name,
                'company_name': post.project.company.name,
                'date': post.scheduled_date.isoformat() if post.scheduled_date else post.scheduled_time.isoformat(),
                'deadline': post.scheduled_date.isoformat() if post.scheduled_date else None,
                'status': post.status,
                'project_id': post.project.id,
                'company_id': post.project.company.id,
                'post_id': post.id
            }

            # Add thumbnail if media exists
            if post.media:
                request_obj = self.request if hasattr(self, 'request') else request
                event_data['thumbnail_url'] = request_obj.build_absolute_uri(post.media.url)
                # Determine media type
                if post.media.name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                    event_data['media_type'] = 'image'
                elif post.media.name.lower().endswith(('.mp4', '.webm', '.ogg', '.avi', '.mov')):
                    event_data['media_type'] = 'video'
                else:
                    event_data['media_type'] = 'file'

            calendar_events.append(event_data)

        return Response({
            'events': calendar_events,
            'projects': [
                {
                    'id': p.id,
                    'name': p.name,
                    'company_name': p.company.name,
                    'company_id': p.company.id
                } for p in projects
            ]
        })

    @action(detail=False, methods=['post'])
    def upload_post(self, request):
        """Create a new post with file upload"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        try:
            # Get the data from the request (don't copy to avoid file pickling issues)
            data = {}
            for key, value in request.data.items():
                if key != 'media':  # Handle file separately
                    data[key] = value

            # Debug logging
            print(f"Received data: {data}")
            print(f"Request user: {request.user}")
            print(f"Files: {list(request.FILES.keys())}")

            # Set the creator to the current user
            data['creator'] = request.user.id

            # Handle file upload
            if 'media' in request.FILES:
                data['media'] = request.FILES['media']

            # Handle scheduled_time format
            scheduled_time = data.get('scheduled_time')
            if scheduled_time:
                from datetime import datetime
                try:
                    # If it's a datetime-local format, convert it to proper datetime
                    if 'T' in scheduled_time:
                        if len(scheduled_time) == 16:  # YYYY-MM-DDTHH:MM
                            scheduled_time += ':00'  # Add seconds
                        # Parse and convert to datetime object
                        parsed_datetime = datetime.fromisoformat(scheduled_time)
                        data['scheduled_time'] = parsed_datetime
                        data['scheduled_date'] = parsed_datetime  # Keep as datetime, not date
                    else:
                        # If it's just a date, use current time
                        date_obj = datetime.strptime(scheduled_time, '%Y-%m-%d')
                        data['scheduled_time'] = date_obj
                        data['scheduled_date'] = date_obj  # Keep as datetime, not date
                except ValueError as e:
                    print(f"Error parsing scheduled_time: {e}")
                    # Use current datetime as fallback
                    now = datetime.now()
                    data['scheduled_time'] = now
                    data['scheduled_date'] = now

            # Validate that the project is assigned to this creator
            project_id = data.get('project')
            if not project_id:
                return Response({'error': 'Project is required.'}, status=400)

            try:
                project = Project.objects.get(id=project_id, creators=request.user)
            except Project.DoesNotExist:
                return Response({'error': 'You are not assigned to this project.'}, status=403)

            # Create the post
            serializer = PostSerializer(data=data, context={'request': request})
            if serializer.is_valid():
                post = serializer.save()
                return Response(PostSerializer(post, context={'request': request}).data, status=201)
            else:
                print(f"Serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=400)

        except Exception as e:
            print(f"Exception in upload_post: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({'error': str(e)}, status=500)

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Update post status (for creators to submit for review)"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(id=pk, creator=request.user)
            new_status = request.data.get('status')

            # Validate status transitions for creators
            allowed_transitions = {
                'draft': ['submitted'],
                'rejected': ['submitted'],
                'rework': ['submitted']
            }

            if post.status not in allowed_transitions or new_status not in allowed_transitions[post.status]:
                return Response({'error': f'Cannot change status from {post.status} to {new_status}'}, status=400)

            post.status = new_status
            post.save()

            return Response({
                'message': 'Post status updated successfully',
                'post': PostSerializer(post, context={'request': request}).data
            })

        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not accessible'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)


class CompanyAdminViewSet(viewsets.ViewSet):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def _check_company_admin_permission(self, request):
        """Check if user is company admin and has access to company data"""
        if request.user.role != 'company_admin':
            return Response({'error': 'Access denied. Company admin role required.'}, status=403)
        if not request.user.company:
            return Response({'error': 'No company associated with this admin.'}, status=400)
        return None

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get dashboard statistics for company admin"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company

        # Project statistics
        total_projects = Project.objects.filter(company=company).count()
        active_projects = Project.objects.filter(company=company, posts__status__in=['draft', 'submitted', 'scheduled']).distinct().count()
        completed_projects = Project.objects.filter(company=company, posts__status='posted').distinct().count()

        # Content statistics
        pending_reviews = Post.objects.filter(project__company=company, status='submitted').count()
        total_posts = Post.objects.filter(project__company=company).count()
        approved_posts = Post.objects.filter(project__company=company, status='posted').count()
        rejected_posts = Post.objects.filter(project__company=company, status='rejected').count()

        # Creator statistics
        total_creators = User.objects.filter(role='creator', assigned_projects__company=company).distinct().count()

        return Response({
            'company_name': company.name,
            'admin_name': request.user.get_full_name() or request.user.username,
            'projects': {
                'total': total_projects,
                'active': active_projects,
                'completed': completed_projects
            },
            'content': {
                'total_posts': total_posts,
                'pending_reviews': pending_reviews,
                'approved': approved_posts,
                'rejected': rejected_posts
            },
            'creators': {
                'total': total_creators
            }
        })

    @action(detail=False, methods=['get'])
    def recent_content(self, request):
        """Get recent content submissions for the company"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company
        recent_posts = Post.objects.filter(
            project__company=company
        ).order_by('-scheduled_time')[:10]

        serializer = PostSerializer(recent_posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def pending_reviews(self, request):
        """Get content pending approval"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company
        pending_posts = Post.objects.filter(
            project__company=company,
            status='submitted'
        ).order_by('-scheduled_time')

        serializer = PostSerializer(pending_posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def creators(self, request):
        """Get all creators assigned to company projects"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company
        creators = User.objects.filter(
            role='creator',
            assigned_projects__company=company
        ).distinct()

        creators_data = []
        for creator in creators:
            # Get creator's projects in this company
            creator_projects = Project.objects.filter(company=company, creators=creator)
            # Get creator's recent posts
            recent_posts_count = Post.objects.filter(
                creator=creator,
                project__company=company
            ).count()
            pending_posts_count = Post.objects.filter(
                creator=creator,
                project__company=company,
                status='submitted'
            ).count()

            creators_data.append({
                'id': creator.id,
                'username': creator.username,
                'full_name': creator.get_full_name() or creator.username,
                'email': creator.email,
                'projects': [{'id': p.id, 'name': p.name} for p in creator_projects],
                'total_posts': recent_posts_count,
                'pending_posts': pending_posts_count,
                'status': 'active' if pending_posts_count > 0 else 'idle'
            })

        return Response(creators_data)

    @action(detail=True, methods=['post'])
    def approve_content(self, request, pk=None):
        """Approve a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company=request.user.company,
                status='submitted'
            )
            post.status = 'posted'
            post.save()

            return Response({
                'message': 'Content approved successfully',
                'post_id': post.id,
                'new_status': post.status
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for approval'}, status=404)

    @action(detail=True, methods=['post'])
    def reject_content(self, request, pk=None):
        """Reject a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company=request.user.company,
                status='submitted'
            )
            post.status = 'rejected'
            post.save()

            return Response({
                'message': 'Content rejected successfully',
                'post_id': post.id,
                'new_status': post.status
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for rejection'}, status=404)

    @action(detail=True, methods=['post'])
    def request_changes(self, request, pk=None):
        """Request changes to a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company=request.user.company,
                status='submitted'
            )
            post.status = 'rework'
            post.save()

            return Response({
                'message': 'Changes requested successfully',
                'post_id': post.id,
                'new_status': post.status
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for changes'}, status=404)
    

    
def custom_exception_handler(exc, context):
    """Custom exception handler with better logging for authentication issues"""
    response = exception_handler(exc, context)

    if response is not None:
        # Log different types of errors with more context
        request = context.get('request')
        user = getattr(request, 'user', None) if request else None

        if response.status_code == 401:
            print(f"🔒 Unauthorized access attempt - User: {user}, Path: {request.path if request else 'unknown'}")
            print(f"    Error details: {response.data}")
        elif response.status_code == 403:
            print(f"🚫 Forbidden access - User: {user}, Path: {request.path if request else 'unknown'}")
            print(f"    Error details: {response.data}")
        else:
            print(f"⚠️  Validation error (Status {response.status_code}):", response.data)

    return response

def health_check(request):
    return JsonResponse({"status": "ok"})