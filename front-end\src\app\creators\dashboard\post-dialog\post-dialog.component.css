.post-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 500px;
  max-width: 600px;
}

.full-width {
  width: 100%;
}

.datetime-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.half-width {
  flex: 1;
}

.file-upload-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px 0;
}

.file-upload-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.file-input {
  padding: 8px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  background-color: #fafafa;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.file-input:hover {
  border-color: #1976d2;
}

.file-upload-hint {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.preview-section {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

mat-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

mat-dialog-actions {
  padding: 16px 24px;
  margin: 0;
}

/* Form validation styles */
.mat-mdc-form-field.mat-form-field-invalid .mat-mdc-text-field-wrapper {
  border-color: #f44336;
}

.mat-error {
  font-size: 12px;
  margin-top: 4px;
}