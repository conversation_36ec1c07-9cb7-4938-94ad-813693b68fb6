#!/usr/bin/env python3
"""
Script to check user passwords and reset them if needed
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from django.contrib.auth import authenticate
from core.models import User

def check_and_fix_users():
    """Check all users and fix authentication issues"""
    print("🔍 Checking all users...")
    
    users = User.objects.all()
    for user in users:
        print(f"\n👤 User: {user.username}")
        print(f"   Role: {user.role}")
        print(f"   Active: {user.is_active}")
        print(f"   Has password: {bool(user.password)}")
        
        # Test authentication with username as password
        auth_user = authenticate(username=user.username, password=user.username)
        if auth_user:
            print(f"   ✅ Authentication works with password: {user.username}")
        else:
            print(f"   ❌ Authentication failed with password: {user.username}")
            print(f"   🔧 Setting password to: {user.username}")
            user.set_password(user.username)
            user.save()
            
            # Test again
            auth_user = authenticate(username=user.username, password=user.username)
            if auth_user:
                print(f"   ✅ Authentication now works!")
            else:
                print(f"   ❌ Authentication still failing!")

if __name__ == "__main__":
    check_and_fix_users()
