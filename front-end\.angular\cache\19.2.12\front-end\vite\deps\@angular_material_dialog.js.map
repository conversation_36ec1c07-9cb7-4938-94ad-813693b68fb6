{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/dialog.mjs", "../../../../../../node_modules/@angular/material/fesm2022/dialog.mjs"], "sourcesContent": ["import { FocusTrapFactory, Interact<PERSON><PERSON><PERSON><PERSON>, FocusMonitor, _IdGenerator, A11yModule } from '@angular/cdk/a11y';\nimport { OverlayRef, Overlay, OverlayContainer, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { Platform, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Renderer2, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/** Configuration for opening a modal dialog. */\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nclass DialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Optional CSS class or classes applied to the overlay panel. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Optional CSS class or classes applied to the overlay backdrop. */\n  backdropClass = '';\n  /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n  disableClose = false;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n  positionStrategy;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Dialog label applied via `aria-label` */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the previously-focused element upon closing.\n   * Has the following behavior based on the type that is passed in:\n   * - `boolean` - when true, will return focus to the element that was focused before the dialog\n   *    was opened, otherwise won't restore focus at all.\n   * - `string` - focus will be restored to the first element that matches the CSS selector.\n   * - `HTMLElement` - focus will be restored to the specific element.\n   */\n  restoreFocus = true;\n  /**\n   * Scroll strategy to be used for the dialog. This determines how\n   * the dialog responds to scrolling underneath the panel element.\n   */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user navigates backwards or forwards through browser\n   * history. This does not apply to navigation via anchor element unless using URL-hash based\n   * routing (`HashLocationStrategy` in the Angular router).\n   */\n  closeOnNavigation = true;\n  /**\n   * Whether the dialog should close when the dialog service is destroyed. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead.\n   */\n  closeOnDestroy = true;\n  /**\n   * Whether the dialog should close when the underlying overlay is detached. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n   * external detachment can happen as a result of a scroll strategy triggering it or when the\n   * browser location changes.\n   */\n  closeOnOverlayDetachments = true;\n  /**\n   * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n   * @deprecated No longer used. Will be removed.\n   * @breaking-change 20.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * Providers that will be exposed to the contents of the dialog. Can also\n   * be provided as a function in order to generate the providers lazily.\n   */\n  providers;\n  /**\n   * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n   * A configuration object can be passed in to customize the providers that will be exposed\n   * to the dialog container.\n   */\n  container;\n  /**\n   * Context that will be passed to template-based dialogs.\n   * A function can be passed in to resolve the context lazily.\n   */\n  templateContext;\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  _config;\n  _interactivityChecker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _overlayRef = inject(OverlayRef);\n  _focusMonitor = inject(FocusMonitor);\n  _renderer = inject(Renderer2);\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** The portal outlet inside of this container into which the dialog content will be loaded. */\n  _portalOutlet;\n  /** The class that traps and manages focus within the dialog. */\n  _focusTrap = null;\n  /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n  _elementFocusedBeforeDialogWasOpened = null;\n  /**\n   * Type of interaction that led to the dialog being closed. This is used to determine\n   * whether the focus style will be applied when returning focus to its original location\n   * after the dialog is closed.\n   */\n  _closeInteractionType = null;\n  /**\n   * Queue of the IDs of the dialog's label element, based on their definition order. The first\n   * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n   * where there are two or more titles in the DOM at a time and the first one is destroyed while\n   * the rest are present.\n   */\n  _ariaLabelledByQueue = [];\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _injector = inject(Injector);\n  _isDestroyed = false;\n  constructor() {\n    super();\n    // Callback is primarily for some internal tests\n    // that were instantiating the dialog container manually.\n    this._config = inject(DialogConfig, {\n      optional: true\n    }) || new DialogConfig();\n    if (this._config.ariaLabelledBy) {\n      this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n    }\n  }\n  _addAriaLabelledBy(id) {\n    this._ariaLabelledByQueue.push(id);\n    this._changeDetectorRef.markForCheck();\n  }\n  _removeAriaLabelledBy(id) {\n    const index = this._ariaLabelledByQueue.indexOf(id);\n    if (index > -1) {\n      this._ariaLabelledByQueue.splice(index, 1);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._isDestroyed = true;\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attaches a DOM portal to the dialog container.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._contentAttached();\n    return result;\n  };\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          deregisterBlur();\n          deregisterMousedown();\n          element.removeAttribute('tabindex');\n        };\n        const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n        const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus() {\n    if (this._isDestroyed) {\n      return;\n    }\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait until after the next render.\n    afterNextRender(() => {\n      const element = this._elementRef.nativeElement;\n      switch (this._config.autoFocus) {\n        case false:\n        case 'dialog':\n          // Ensure that focus is on the dialog container. It's possible that a different\n          // component tried to move focus while the open animation was running. See:\n          // https://github.com/angular/components/issues/16215. Note that we only want to do this\n          // if the focus isn't inside the dialog already, because it's possible that the consumer\n          // turned off `autoFocus` in order to move focus themselves.\n          if (!this._containsFocus()) {\n            element.focus();\n          }\n          break;\n        case true:\n        case 'first-tabbable':\n          const focusedSuccessfully = this._focusTrap?.focusInitialElement();\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer();\n          }\n          break;\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n          break;\n        default:\n          this._focusByCssSelector(this._config.autoFocus);\n          break;\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer() {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n  static ɵfac = function CdkDialogContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDialogContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkDialogContainer,\n    selectors: [[\"cdk-dialog-container\"]],\n    viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n      }\n    },\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n    hostVars: 6,\n    hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkPortalOutlet\", \"\"]],\n    template: function CdkDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet />\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  overlayRef;\n  config;\n  /**\n   * Instance of component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Instance of the container that is rendering out the dialog content. */\n  containerInstance;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Emits when the dialog has been closed. */\n  closed = new Subject();\n  /** Emits when the backdrop of the dialog is clicked. */\n  backdropClick;\n  /** Emits when on keyboard events within the dialog. */\n  keydownEvents;\n  /** Emits on pointer events that happen outside of the dialog. */\n  outsidePointerEvents;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subscription to external detachments of the dialog. */\n  _detachSubscription;\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass Dialog {\n  _overlay = inject(Overlay);\n  _injector = inject(Injector);\n  _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, {\n    optional: true\n  });\n  _parentDialog = inject(Dialog, {\n    optional: true,\n    skipSelf: true\n  });\n  _overlayContainer = inject(OverlayContainer);\n  _idGenerator = inject(_IdGenerator);\n  _openDialogsAtThisLevel = [];\n  _afterAllClosedAtThisLevel = new Subject();\n  _afterOpenedAtThisLevel = new Subject();\n  _ariaHiddenElements = new Map();\n  _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n  constructor() {}\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = {\n      ...defaults,\n      ...config\n    };\n    config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }));\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n      dialogRef.componentRef = contentRef;\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  static ɵfac = function Dialog_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Dialog)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Dialog,\n    factory: Dialog.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nclass DialogModule {\n  static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DialogModule,\n    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n    exports: [\n    // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n    // don't have to remember to import it or be faced with an unhelpful error.\n    PortalModule, CdkDialogContainer]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Dialog],\n    imports: [OverlayModule, PortalModule, A11yModule,\n    // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n    // don't have to remember to import it or be faced with an unhelpful error.\n    PortalModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      providers: [Dialog]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };\n", "import { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injectable, ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nfunction MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nclass MatDialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Custom class for the overlay pane. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Custom class for the backdrop. */\n  backdropClass = '';\n  /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n  disableClose = false;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Position overrides. */\n  position;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Aria label to assign to the dialog element. */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the\n   * previously-focused element, after it's closed.\n   */\n  restoreFocus = true;\n  /** Whether to wait for the opening animation to finish before trapping focus. */\n  delayFocusTrap = true;\n  /** Scroll strategy to be used for the dialog. */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  closeOnNavigation = true;\n  /**\n   * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n   * @deprecated No longer used. Will be removed.\n   * @breaking-change 20.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * Duration of the enter animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  enterAnimationDuration;\n  /**\n   * Duration of the exit animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  exitAnimationDuration;\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  /** Emits when an animation state changes. */\n  _animationStateChanged = new EventEmitter();\n  /** Whether animations are enabled. */\n  _animationsEnabled = this._animationMode !== 'NoopAnimations';\n  /** Number of actions projected in the dialog. */\n  _actionSectionCount = 0;\n  /** Host element of the dialog container component. */\n  _hostElement = this._elementRef.nativeElement;\n  /** Duration of the dialog open animation. */\n  _enterAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION : 0;\n  /** Duration of the dialog close animation. */\n  _exitAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION : 0;\n  /** Current timer for dialog animations. */\n  _animationTimer = null;\n  _contentAttached() {\n    // Delegate to the original dialog-container initialization (i.e. saving the\n    // previous element, setting up the focus trap and moving focus to the container).\n    super._contentAttached();\n    // Note: Usually we would be able to use the MDC dialog foundation here to handle\n    // the dialog animation for us, but there are a few reasons why we just leverage\n    // their styles and not use the runtime foundation code:\n    //   1. Foundation does not allow us to disable animations.\n    //   2. Foundation contains unnecessary features we don't need and aren't\n    //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n    this._startOpenAnimation();\n  }\n  /** Starts the dialog open animation if enabled. */\n  _startOpenAnimation() {\n    this._animationStateChanged.emit({\n      state: 'opening',\n      totalTime: this._enterAnimationDuration\n    });\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      // One would expect that the open class is added once the animation finished, but MDC\n      // uses the open class in combination with the opening class to start the animation.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n      this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n    } else {\n      this._hostElement.classList.add(OPEN_CLASS);\n      // Note: We could immediately finish the dialog opening here with noop animations,\n      // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n      // Executing this immediately would mean that `afterOpened` emits synchronously\n      // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n      Promise.resolve().then(() => this._finishDialogOpen());\n    }\n  }\n  /**\n   * Starts the exit animation of the dialog if enabled. This method is\n   * called by the dialog ref.\n   */\n  _startExitAnimation() {\n    this._animationStateChanged.emit({\n      state: 'closing',\n      totalTime: this._exitAnimationDuration\n    });\n    this._hostElement.classList.remove(OPEN_CLASS);\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n      this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n    } else {\n      // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n      // set up before any user can subscribe to the backdrop click. The subscription triggers\n      // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n      // animation state event if animations are disabled, the overlay would be disposed\n      // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n      // skipped. We work around this by waiting with the dialog close until the next tick when\n      // all subscriptions have been fired as expected. This is not an ideal solution, but\n      // there doesn't seem to be any other good way. Alternatives that have been considered:\n      //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n      //      Also this issue is specific to the MDC implementation where the dialog could\n      //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n      //      and closing always takes at least a tick.\n      //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n      //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n      //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n      // Based on the fact that this is specific to the MDC-based implementation of the dialog\n      // animations, the defer is applied here.\n      Promise.resolve().then(() => this._finishDialogClose());\n    }\n  }\n  /**\n   * Updates the number action sections.\n   * @param delta Increase/decrease in the number of sections.\n   */\n  _updateActionSectionCount(delta) {\n    this._actionSectionCount += delta;\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Completes the dialog open by clearing potential animation classes, trapping\n   * focus and emitting an opened event.\n   */\n  _finishDialogOpen = () => {\n    this._clearAnimationClasses();\n    this._openAnimationDone(this._enterAnimationDuration);\n  };\n  /**\n   * Completes the dialog close by clearing potential animation classes, restoring\n   * focus and emitting a closed event.\n   */\n  _finishDialogClose = () => {\n    this._clearAnimationClasses();\n    this._animationStateChanged.emit({\n      state: 'closed',\n      totalTime: this._exitAnimationDuration\n    });\n  };\n  /** Clears all dialog animation classes. */\n  _clearAnimationClasses() {\n    this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n  }\n  _waitForAnimationToComplete(duration, callback) {\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n    // Note that we want this timer to run inside the NgZone, because we want\n    // the related events like `afterClosed` to be inside the zone as well.\n    this._animationTimer = setTimeout(callback, duration);\n  }\n  /** Runs a callback in `requestAnimationFrame`, if available. */\n  _requestAnimationFrame(callback) {\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(callback);\n      } else {\n        callback();\n      }\n    });\n  }\n  _captureInitialFocus() {\n    if (!this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Callback for when the open dialog animation has finished. Intended to\n   * be called by sub-classes that use different animation implementations.\n   */\n  _openAnimationDone(totalTime) {\n    if (this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n    this._animationStateChanged.next({\n      state: 'opened',\n      totalTime\n    });\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n  }\n  attachComponentPortal(portal) {\n    // When a component is passed into the dialog, the host element interrupts\n    // the `display:flex` from affecting the dialog title, content, and\n    // actions. To fix this, we make the component host `display: contents` by\n    // marking its host with the `mat-mdc-dialog-component-host` class.\n    //\n    // Note that this problem does not exist when a template ref is used since\n    // the title, contents, and actions are then nested directly under the\n    // dialog surface.\n    const ref = super.attachComponentPortal(portal);\n    ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n    return ref;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogContainer_BaseFactory;\n    return function MatDialogContainer_Factory(__ngFactoryType__) {\n      return (ɵMatDialogContainer_BaseFactory || (ɵMatDialogContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogContainer)))(__ngFactoryType__ || MatDialogContainer);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDialogContainer,\n    selectors: [[\"mat-dialog-container\"]],\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n    hostVars: 10,\n    hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx._config.id);\n        i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled)(\"mat-mdc-dialog-container-with-actions\", ctx._actionSectionCount > 0);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"mat-mdc-dialog-inner-container\", \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mat-mdc-dialog-container mdc-dialog',\n        'tabindex': '-1',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[id]': '_config.id',\n        '[attr.role]': '_config.role',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n        '[class._mat-animation-noopable]': '!_animationsEnabled',\n        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0'\n      },\n      template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\"]\n    }]\n  }], null, null);\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\nvar MatDialogState;\n(function (MatDialogState) {\n  MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n  MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n  MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  _ref;\n  _containerInstance;\n  /** The instance of component opened into the dialog. */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subject for notifying the user that the dialog has finished opening. */\n  _afterOpened = new Subject();\n  /** Subject for notifying the user that the dialog has started closing. */\n  _beforeClosed = new Subject();\n  /** Result to be passed to afterClosed. */\n  _result;\n  /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n  _closeFallbackTimeout;\n  /** Current state of the dialog. */\n  _state = MatDialogState.OPEN;\n  // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n  // already has a second `options` parameter that we can use. The problem is that internal tests\n  // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n  // because it'll be called with two arguments by things like `MatDialogClose`.\n  /** Interaction that caused the dialog to close. */\n  _closeInteractionType;\n  constructor(_ref, config, _containerInstance) {\n    this._ref = _ref;\n    this._containerInstance = _containerInstance;\n    this.disableClose = config.disableClose;\n    this.id = _ref.id;\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n  _overlay = inject(Overlay);\n  _defaultOptions = inject(MAT_DIALOG_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _scrollStrategy = inject(MAT_DIALOG_SCROLL_STRATEGY);\n  _parentDialog = inject(MatDialog, {\n    optional: true,\n    skipSelf: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  _dialog = inject(Dialog);\n  _openDialogsAtThisLevel = [];\n  _afterAllClosedAtThisLevel = new Subject();\n  _afterOpenedAtThisLevel = new Subject();\n  dialogConfigClass = MatDialogConfig;\n  _dialogRefConstructor;\n  _dialogContainerType;\n  _dialogDataToken;\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n  constructor() {\n    this._dialogRefConstructor = MatDialogRef;\n    this._dialogContainerType = MatDialogContainer;\n    this._dialogDataToken = MAT_DIALOG_DATA;\n  }\n  open(componentOrTemplateRef, config) {\n    let dialogRef;\n    config = {\n      ...(this._defaultOptions || new MatDialogConfig()),\n      ...config\n    };\n    config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n    config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n    const cdkRef = this._dialog.open(componentOrTemplateRef, {\n      ...config,\n      positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Disable closing on destroy, because this service cleans up its open dialogs as well.\n      // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n      // the dialogs immediately whereas we want it to wait for the animations to finish.\n      closeOnDestroy: false,\n      // Disable closing on detachments so that we can sync up the animation.\n      // The Material dialog ref handles this manually.\n      closeOnOverlayDetachments: false,\n      container: {\n        type: this._dialogContainerType,\n        providers: () => [\n        // Provide our config as the CDK config as well since it has the same interface as the\n        // CDK one, but it contains the actual values passed in by the user for things like\n        // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n        {\n          provide: this.dialogConfigClass,\n          useValue: config\n        }, {\n          provide: DialogConfig,\n          useValue: config\n        }]\n      },\n      templateContext: () => ({\n        dialogRef\n      }),\n      providers: (ref, cdkConfig, dialogContainer) => {\n        dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n        dialogRef.updatePosition(config?.position);\n        return [{\n          provide: this._dialogContainerType,\n          useValue: dialogContainer\n        }, {\n          provide: this._dialogDataToken,\n          useValue: cdkConfig.data\n        }, {\n          provide: this._dialogRefConstructor,\n          useValue: dialogRef\n        }];\n      }\n    });\n    // This can't be assigned in the `providers` callback, because\n    // the instance hasn't been assigned to the CDK ref yet.\n    dialogRef.componentRef = cdkRef.componentRef;\n    dialogRef.componentInstance = cdkRef.componentInstance;\n    this.openDialogs.push(dialogRef);\n    this.afterOpened.next(dialogRef);\n    dialogRef.afterClosed().subscribe(() => {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        if (!this.openDialogs.length) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    });\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    this._closeDialogs(this.openDialogs);\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Only close the dialogs at this level on destroy\n    // since the parent service may still be active.\n    this._closeDialogs(this._openDialogsAtThisLevel);\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n  }\n  _closeDialogs(dialogs) {\n    let i = dialogs.length;\n    while (i--) {\n      dialogs[i].close();\n    }\n  }\n  static ɵfac = function MatDialog_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialog)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatDialog,\n    factory: MatDialog.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n  dialogRef = inject(MatDialogRef, {\n    optional: true\n  });\n  _elementRef = inject(ElementRef);\n  _dialog = inject(MatDialog);\n  /** Screen-reader label for the button. */\n  ariaLabel;\n  /** Default to \"button\" to prevents accidental form submits. */\n  type = 'button';\n  /** Dialog close input. */\n  dialogResult;\n  _matDialogClose;\n  constructor() {}\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n  }\n  ngOnChanges(changes) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n  _onButtonClick(event) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n  }\n  static ɵfac = function MatDialogClose_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogClose)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogClose,\n    selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n    hostVars: 2,\n    hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n          return ctx._onButtonClick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      type: \"type\",\n      dialogResult: [0, \"mat-dialog-close\", \"dialogResult\"],\n      _matDialogClose: [0, \"matDialogClose\", \"_matDialogClose\"]\n    },\n    exportAs: [\"matDialogClose\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogClose, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-close], [matDialogClose]',\n      exportAs: 'matDialogClose',\n      host: {\n        '(click)': '_onButtonClick($event)',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.type]': 'type'\n      }\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    type: [{\n      type: Input\n    }],\n    dialogResult: [{\n      type: Input,\n      args: ['mat-dialog-close']\n    }],\n    _matDialogClose: [{\n      type: Input,\n      args: ['matDialogClose']\n    }]\n  });\n})();\nclass MatDialogLayoutSection {\n  _dialogRef = inject(MatDialogRef, {\n    optional: true\n  });\n  _elementRef = inject(ElementRef);\n  _dialog = inject(MatDialog);\n  constructor() {}\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        this._onAdd();\n      });\n    }\n  }\n  ngOnDestroy() {\n    // Note: we null check because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    const instance = this._dialogRef?._containerInstance;\n    if (instance) {\n      Promise.resolve().then(() => {\n        this._onRemove();\n      });\n    }\n  }\n  static ɵfac = function MatDialogLayoutSection_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogLayoutSection)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogLayoutSection\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogLayoutSection, [{\n    type: Directive\n  }], () => [], null);\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n  id = inject(_IdGenerator).getId('mat-mdc-dialog-title-');\n  _onAdd() {\n    // Note: we null check the queue, because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n  }\n  _onRemove() {\n    this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogTitle_BaseFactory;\n    return function MatDialogTitle_Factory(__ngFactoryType__) {\n      return (ɵMatDialogTitle_BaseFactory || (ɵMatDialogTitle_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogTitle)))(__ngFactoryType__ || MatDialogTitle);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogTitle,\n    selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n    hostVars: 1,\n    hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matDialogTitle\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-title], [matDialogTitle]',\n      exportAs: 'matDialogTitle',\n      host: {\n        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n        '[id]': 'id'\n      }\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n  static ɵfac = function MatDialogContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogContent,\n    selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"],\n    features: [i0.ɵɵHostDirectivesFeature([i1.CdkScrollable])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n      host: {\n        'class': 'mat-mdc-dialog-content mdc-dialog__content'\n      },\n      hostDirectives: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n  /**\n   * Horizontal alignment of action buttons.\n   */\n  align;\n  _onAdd() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n  }\n  _onRemove() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogActions_BaseFactory;\n    return function MatDialogActions_Factory(__ngFactoryType__) {\n      return (ɵMatDialogActions_BaseFactory || (ɵMatDialogActions_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogActions)))(__ngFactoryType__ || MatDialogActions);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogActions,\n    selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n    hostVars: 6,\n    hostBindings: function MatDialogActions_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-start\", ctx.align === \"start\")(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n      }\n    },\n    inputs: {\n      align: \"align\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogActions, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n      host: {\n        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nconst DIRECTIVES = [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent];\nclass MatDialogModule {\n  static ɵfac = function MatDialogModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDialogModule,\n    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent],\n    exports: [MatCommonModule, MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatDialog],\n    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatDialog]\n    }]\n  }], null, null);\n})();\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n * @deprecated Will stop being exported.\n * @breaking-change 21.0.0\n */\nconst _defaultParams = {\n  params: {\n    enterAnimationDuration: '150ms',\n    exitAnimationDuration: '75ms'\n  }\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDialogAnimations = {\n  // Represents:\n  // trigger('dialogContainer', [\n  //   // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  //   // specifying the transform explicitly, causes IE both to blur the dialog content and\n  //   // decimate the animation performance. Leaving it as `none` solves both issues.\n  //   state('void, exit', style({opacity: 0, transform: 'scale(0.7)'})),\n  //   state('enter', style({transform: 'none'})),\n  //   transition(\n  //     '* => enter',\n  //     group([\n  //       animate(\n  //         '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)',\n  //         style({transform: 'none', opacity: 1}),\n  //       ),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     _defaultParams,\n  //   ),\n  //   transition(\n  //     '* => void, * => exit',\n  //     group([\n  //       animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({opacity: 0})),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     _defaultParams,\n  //   ),\n  // ])\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: {\n    type: 7,\n    name: 'dialogContainer',\n    definitions: [{\n      type: 0,\n      name: 'void, exit',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.7)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'enter',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => enter',\n      animation: {\n        type: 3,\n        steps: [{\n          type: 4,\n          styles: {\n            type: 6,\n            styles: {\n              transform: 'none',\n              opacity: 1\n            },\n            offset: null\n          },\n          timings: '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)'\n        }, {\n          type: 11,\n          selector: '@*',\n          animation: {\n            type: 9,\n            options: null\n          },\n          options: {\n            optional: true\n          }\n        }],\n        options: null\n      },\n      options: {\n        params: {\n          enterAnimationDuration: '150ms',\n          exitAnimationDuration: '75ms'\n        }\n      }\n    }, {\n      type: 1,\n      expr: '* => void, * => exit',\n      animation: {\n        type: 3,\n        steps: [{\n          type: 4,\n          styles: {\n            type: 6,\n            styles: {\n              opacity: 0\n            },\n            offset: null\n          },\n          timings: '{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'\n        }, {\n          type: 11,\n          selector: '@*',\n          animation: {\n            type: 9,\n            options: null\n          },\n          options: {\n            optional: true\n          }\n        }],\n        options: null\n      },\n      options: {\n        params: {\n          enterAnimationDuration: '150ms',\n          exitAnimationDuration: '75ms'\n        }\n      }\n    }],\n    options: {}\n  }\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogState, MatDialogTitle, _closeDialogVia, _defaultParams, matDialogAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,SAAS,0CAA0C,IAAI,KAAK;AAAC;AAC7D,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,aAAa;AAAA;AAAA,EAEb,cAAc;AAAA;AAAA,EAEd,gBAAgB;AAAA;AAAA,EAEhB,eAAe;AAAA;AAAA,EAEf,QAAQ;AAAA;AAAA,EAER,SAAS;AAAA;AAAA,EAET;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASZ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AACF;AACA,SAAS,yCAAyC;AAChD,QAAM,MAAM,uEAAuE;AACrF;AAKA,IAAM,qBAAN,MAAM,4BAA2B,iBAAiB;AAAA,EAChD,cAAc,OAAO,UAAU;AAAA,EAC/B,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C;AAAA,EACA,wBAAwB,OAAO,oBAAoB;AAAA,EACnD,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,YAAY;AAAA,EACnC,YAAY,OAAO,SAAS;AAAA,EAC5B,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb,uCAAuC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvC,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,uBAAuB,CAAC;AAAA,EACxB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,YAAY,OAAO,QAAQ;AAAA,EAC3B,eAAe;AAAA,EACf,cAAc;AACZ,UAAM;AAGN,SAAK,UAAU,OAAO,cAAc;AAAA,MAClC,UAAU;AAAA,IACZ,CAAC,KAAK,IAAI,aAAa;AACvB,QAAI,KAAK,QAAQ,gBAAgB;AAC/B,WAAK,qBAAqB,KAAK,KAAK,QAAQ,cAAc;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,mBAAmB,IAAI;AACrB,SAAK,qBAAqB,KAAK,EAAE;AACjC,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,sBAAsB,IAAI;AACxB,UAAM,QAAQ,KAAK,qBAAqB,QAAQ,EAAE;AAClD,QAAI,QAAQ,IAAI;AACd,WAAK,qBAAqB,OAAO,OAAO,CAAC;AACzC,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,QAAQ;AAC5B,QAAI,KAAK,cAAc,YAAY,MAAM,OAAO,cAAc,eAAe,YAAY;AACvF,6CAAuC;AAAA,IACzC;AACA,UAAM,SAAS,KAAK,cAAc,sBAAsB,MAAM;AAC9D,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,QAAQ;AAC3B,QAAI,KAAK,cAAc,YAAY,MAAM,OAAO,cAAc,eAAe,YAAY;AACvF,6CAAuC;AAAA,IACzC;AACA,UAAM,SAAS,KAAK,cAAc,qBAAqB,MAAM;AAC7D,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAU;AAC1B,QAAI,KAAK,cAAc,YAAY,MAAM,OAAO,cAAc,eAAe,YAAY;AACvF,6CAAuC;AAAA,IACzC;AACA,UAAM,SAAS,KAAK,cAAc,gBAAgB,MAAM;AACxD,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,kBAAkB;AAChB,QAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,SAAS;AAC5B,QAAI,CAAC,KAAK,sBAAsB,YAAY,OAAO,GAAG;AACpD,cAAQ,WAAW;AAEnB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,WAAW,MAAM;AACrB,yBAAe;AACf,8BAAoB;AACpB,kBAAQ,gBAAgB,UAAU;AAAA,QACpC;AACA,cAAM,iBAAiB,KAAK,UAAU,OAAO,SAAS,QAAQ,QAAQ;AACtE,cAAM,sBAAsB,KAAK,UAAU,OAAO,SAAS,aAAa,QAAQ;AAAA,MAClF,CAAC;AAAA,IACH;AACA,YAAQ,MAAM,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAU,SAAS;AACrC,QAAI,iBAAiB,KAAK,YAAY,cAAc,cAAc,QAAQ;AAC1E,QAAI,gBAAgB;AAClB,WAAK,YAAY,gBAAgB,OAAO;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,QAAI,KAAK,cAAc;AACrB;AAAA,IACF;AAIA,oBAAgB,MAAM;AACpB,YAAM,UAAU,KAAK,YAAY;AACjC,cAAQ,KAAK,QAAQ,WAAW;AAAA,QAC9B,KAAK;AAAA,QACL,KAAK;AAMH,cAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,oBAAQ,MAAM;AAAA,UAChB;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,sBAAsB,KAAK,YAAY,oBAAoB;AAGjE,cAAI,CAAC,qBAAqB;AACxB,iBAAK,sBAAsB;AAAA,UAC7B;AACA;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,0CAA0C;AACnE;AAAA,QACF;AACE,eAAK,oBAAoB,KAAK,QAAQ,SAAS;AAC/C;AAAA,MACJ;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,cAAc,KAAK,QAAQ;AACjC,QAAI,qBAAqB;AACzB,QAAI,OAAO,gBAAgB,UAAU;AACnC,2BAAqB,KAAK,UAAU,cAAc,WAAW;AAAA,IAC/D,WAAW,OAAO,gBAAgB,WAAW;AAC3C,2BAAqB,cAAc,KAAK,uCAAuC;AAAA,IACjF,WAAW,aAAa;AACtB,2BAAqB;AAAA,IACvB;AAEA,QAAI,KAAK,QAAQ,gBAAgB,sBAAsB,OAAO,mBAAmB,UAAU,YAAY;AACrG,YAAM,gBAAgB,kCAAkC;AACxD,YAAM,UAAU,KAAK,YAAY;AAKjC,UAAI,CAAC,iBAAiB,kBAAkB,KAAK,UAAU,QAAQ,kBAAkB,WAAW,QAAQ,SAAS,aAAa,GAAG;AAC3H,YAAI,KAAK,eAAe;AACtB,eAAK,cAAc,SAAS,oBAAoB,KAAK,qBAAqB;AAC1E,eAAK,wBAAwB;AAAA,QAC/B,OAAO;AACL,6BAAmB,MAAM;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAEtB,QAAI,KAAK,YAAY,cAAc,OAAO;AACxC,WAAK,YAAY,cAAc,MAAM;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,gBAAgB,kCAAkC;AACxD,WAAO,YAAY,iBAAiB,QAAQ,SAAS,aAAa;AAAA,EACpE;AAAA;AAAA,EAEA,uBAAuB;AACrB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,aAAa,KAAK,kBAAkB,OAAO,KAAK,YAAY,aAAa;AAG9E,UAAI,KAAK,WAAW;AAClB,aAAK,uCAAuC,kCAAkC;AAAA,MAChF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAGtB,SAAK,YAAY,cAAc,EAAE,UAAU,MAAM;AAC/C,UAAI,KAAK,QAAQ,cAAc;AAC7B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC;AAAA,MACnC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,YAAY,MAAM,GAAG,sBAAsB;AAAA,IACvD,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,IAAI,QAAQ,MAAM,IAAI,EAAE,QAAQ,IAAI,QAAQ,IAAI,EAAE,cAAc,IAAI,QAAQ,SAAS,EAAE,mBAAmB,IAAI,QAAQ,YAAY,OAAO,IAAI,qBAAqB,CAAC,CAAC,EAAE,cAAc,IAAI,QAAQ,SAAS,EAAE,oBAAoB,IAAI,QAAQ,mBAAmB,IAAI;AAAA,MACzR;AAAA,IACF;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,CAAC;AAAA,IAChC,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,CAAC;AAAA,MACpF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,QAAQ,CAAC,mGAAmG;AAAA,IAC5G,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,eAAe;AAAA,MACzB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,MAC7B;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,mGAAmG;AAAA,IAC9G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,YAAY,QAAQ;AAC9B,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,eAAe,OAAO;AAC3B,SAAK,gBAAgB,WAAW,cAAc;AAC9C,SAAK,gBAAgB,WAAW,cAAc;AAC9C,SAAK,uBAAuB,WAAW,qBAAqB;AAC5D,SAAK,KAAK,OAAO;AACjB,SAAK,cAAc,UAAU,WAAS;AACpC,UAAI,MAAM,YAAY,UAAU,CAAC,KAAK,gBAAgB,CAAC,eAAe,KAAK,GAAG;AAC5E,cAAM,eAAe;AACrB,aAAK,MAAM,QAAW;AAAA,UACpB,aAAa;AAAA,QACf,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,cAAc,UAAU,MAAM;AACjC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,MAAM,QAAW;AAAA,UACpB,aAAa;AAAA,QACf,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,sBAAsB,WAAW,YAAY,EAAE,UAAU,MAAM;AAElE,UAAI,OAAO,8BAA8B,OAAO;AAC9C,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,SAAS;AACrB,QAAI,KAAK,mBAAmB;AAC1B,YAAM,gBAAgB,KAAK;AAC3B,WAAK,kBAAkB,wBAAwB,SAAS,eAAe;AAGvE,WAAK,oBAAoB,YAAY;AACrC,WAAK,WAAW,QAAQ;AACxB,oBAAc,KAAK,MAAM;AACzB,oBAAc,SAAS;AACvB,WAAK,oBAAoB,KAAK,oBAAoB;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,WAAW,eAAe;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,QAAQ,IAAI,SAAS,IAAI;AAClC,SAAK,WAAW,WAAW;AAAA,MACzB;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,SAAS;AACrB,SAAK,WAAW,cAAc,OAAO;AACrC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,SAAK,WAAW,iBAAiB,OAAO;AACxC,WAAO;AAAA,EACT;AACF;AAGA,IAAM,yBAAyB,IAAI,eAAe,wBAAwB;AAAA,EACxE,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,MAAM;AAAA,EAC9C;AACF,CAAC;AAED,IAAM,cAAc,IAAI,eAAe,YAAY;AAEnD,IAAM,wBAAwB,IAAI,eAAe,qBAAqB;AAmBtE,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,WAAW,OAAO,OAAO;AAAA,EACzB,YAAY,OAAO,QAAQ;AAAA,EAC3B,kBAAkB,OAAO,uBAAuB;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB,OAAO,SAAQ;AAAA,IAC7B,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,eAAe,OAAO,YAAY;AAAA,EAClC,0BAA0B,CAAC;AAAA,EAC3B,6BAA6B,IAAI,QAAQ;AAAA,EACzC,0BAA0B,IAAI,QAAQ;AAAA,EACtC,sBAAsB,oBAAI,IAAI;AAAA,EAC9B,kBAAkB,OAAO,sBAAsB;AAAA;AAAA,EAE/C,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,cAAc,cAAc,KAAK;AAAA,EACpE;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,cAAc,cAAc,KAAK;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM,MAAM,KAAK,YAAY,SAAS,KAAK,mBAAmB,IAAI,KAAK,mBAAmB,EAAE,KAAK,UAAU,MAAS,CAAC,CAAC;AAAA,EACvI,cAAc;AAAA,EAAC;AAAA,EACf,KAAK,wBAAwB,QAAQ;AACnC,UAAM,WAAW,KAAK,mBAAmB,IAAI,aAAa;AAC1D,aAAS,kCACJ,WACA;AAEL,WAAO,KAAK,OAAO,MAAM,KAAK,aAAa,MAAM,aAAa;AAC9D,QAAI,OAAO,MAAM,KAAK,cAAc,OAAO,EAAE,MAAM,OAAO,cAAc,eAAe,YAAY;AACjG,YAAM,MAAM,mBAAmB,OAAO,EAAE,iDAAiD;AAAA,IAC3F;AACA,UAAM,gBAAgB,KAAK,kBAAkB,MAAM;AACnD,UAAM,aAAa,KAAK,SAAS,OAAO,aAAa;AACrD,UAAM,YAAY,IAAI,UAAU,YAAY,MAAM;AAClD,UAAM,kBAAkB,KAAK,iBAAiB,YAAY,WAAW,MAAM;AAC3E,cAAU,oBAAoB;AAC9B,SAAK,qBAAqB,wBAAwB,WAAW,iBAAiB,MAAM;AAEpF,QAAI,CAAC,KAAK,YAAY,QAAQ;AAC5B,WAAK,6CAA6C;AAAA,IACpD;AACA,SAAK,YAAY,KAAK,SAAS;AAC/B,cAAU,OAAO,UAAU,MAAM,KAAK,kBAAkB,WAAW,IAAI,CAAC;AACxE,SAAK,YAAY,KAAK,SAAS;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,mBAAe,KAAK,aAAa,YAAU,OAAO,MAAM,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,IAAI;AAChB,WAAO,KAAK,YAAY,KAAK,YAAU,OAAO,OAAO,EAAE;AAAA,EACzD;AAAA,EACA,cAAc;AAIZ,mBAAe,KAAK,yBAAyB,YAAU;AAErD,UAAI,OAAO,OAAO,mBAAmB,OAAO;AAC1C,aAAK,kBAAkB,QAAQ,KAAK;AAAA,MACtC;AAAA,IACF,CAAC;AAID,mBAAe,KAAK,yBAAyB,YAAU,OAAO,MAAM,CAAC;AACrE,SAAK,2BAA2B,SAAS;AACzC,SAAK,wBAAwB,SAAS;AACtC,SAAK,0BAA0B,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,QAAQ;AACxB,UAAM,QAAQ,IAAI,cAAc;AAAA,MAC9B,kBAAkB,OAAO,oBAAoB,KAAK,SAAS,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,iBAAiB;AAAA,MACrH,gBAAgB,OAAO,kBAAkB,KAAK,gBAAgB;AAAA,MAC9D,YAAY,OAAO;AAAA,MACnB,aAAa,OAAO;AAAA,MACpB,WAAW,OAAO;AAAA,MAClB,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,MACf,qBAAqB,OAAO;AAAA,IAC9B,CAAC;AACD,QAAI,OAAO,eAAe;AACxB,YAAM,gBAAgB,OAAO;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,SAAS,WAAW,QAAQ;AAC3C,UAAM,eAAe,OAAO,YAAY,OAAO,kBAAkB;AACjE,UAAM,YAAY,CAAC;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,QAAI;AACJ,QAAI,OAAO,WAAW;AACpB,UAAI,OAAO,OAAO,cAAc,YAAY;AAC1C,wBAAgB,OAAO;AAAA,MACzB,OAAO;AACL,wBAAgB,OAAO,UAAU;AACjC,kBAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,CAAC;AAAA,MACtD;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AACA,UAAM,kBAAkB,IAAI,gBAAgB,eAAe,OAAO,kBAAkB,SAAS,OAAO;AAAA,MAClG,QAAQ,gBAAgB,KAAK;AAAA,MAC7B;AAAA,IACF,CAAC,CAAC;AACF,UAAM,eAAe,QAAQ,OAAO,eAAe;AACnD,WAAO,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,wBAAwB,WAAW,iBAAiB,QAAQ;AAC/E,QAAI,kCAAkC,aAAa;AACjD,YAAM,WAAW,KAAK,gBAAgB,QAAQ,WAAW,iBAAiB,MAAS;AACnF,UAAI,UAAU;AAAA,QACZ,WAAW,OAAO;AAAA,QAClB;AAAA,MACF;AACA,UAAI,OAAO,iBAAiB;AAC1B,kBAAU,kCACL,UACC,OAAO,OAAO,oBAAoB,aAAa,OAAO,gBAAgB,IAAI,OAAO;AAAA,MAEzF;AACA,sBAAgB,qBAAqB,IAAI,eAAe,wBAAwB,MAAM,SAAS,QAAQ,CAAC;AAAA,IAC1G,OAAO;AACL,YAAM,WAAW,KAAK,gBAAgB,QAAQ,WAAW,iBAAiB,KAAK,SAAS;AACxF,YAAM,aAAa,gBAAgB,sBAAsB,IAAI,gBAAgB,wBAAwB,OAAO,kBAAkB,QAAQ,CAAC;AACvI,gBAAU,eAAe;AACzB,gBAAU,oBAAoB,WAAW;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,gBAAgB,QAAQ,WAAW,iBAAiB,kBAAkB;AACpE,UAAM,eAAe,OAAO,YAAY,OAAO,kBAAkB;AACjE,UAAM,YAAY,CAAC;AAAA,MACjB,SAAS;AAAA,MACT,UAAU,OAAO;AAAA,IACnB,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,OAAO,WAAW;AACpB,UAAI,OAAO,OAAO,cAAc,YAAY;AAC1C,kBAAU,KAAK,GAAG,OAAO,UAAU,WAAW,QAAQ,eAAe,CAAC;AAAA,MACxE,OAAO;AACL,kBAAU,KAAK,GAAG,OAAO,SAAS;AAAA,MACpC;AAAA,IACF;AACA,QAAI,OAAO,cAAc,CAAC,gBAAgB,CAAC,aAAa,IAAI,gBAAgB,MAAM;AAAA,MAChF,UAAU;AAAA,IACZ,CAAC,IAAI;AACH,gBAAU,KAAK;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,UACR,OAAO,OAAO;AAAA,UACd,QAAQ,GAAG;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,SAAS,OAAO;AAAA,MACrB,QAAQ,gBAAgB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,WAAW,WAAW;AACtC,UAAM,QAAQ,KAAK,YAAY,QAAQ,SAAS;AAChD,QAAI,QAAQ,IAAI;AACd,WAAK,YAAY,OAAO,OAAO,CAAC;AAGhC,UAAI,CAAC,KAAK,YAAY,QAAQ;AAC5B,aAAK,oBAAoB,QAAQ,CAAC,eAAe,YAAY;AAC3D,cAAI,eAAe;AACjB,oBAAQ,aAAa,eAAe,aAAa;AAAA,UACnD,OAAO;AACL,oBAAQ,gBAAgB,aAAa;AAAA,UACvC;AAAA,QACF,CAAC;AACD,aAAK,oBAAoB,MAAM;AAC/B,YAAI,WAAW;AACb,eAAK,mBAAmB,EAAE,KAAK;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,+CAA+C;AAC7C,UAAM,mBAAmB,KAAK,kBAAkB,oBAAoB;AAEpE,QAAI,iBAAiB,eAAe;AAClC,YAAM,WAAW,iBAAiB,cAAc;AAChD,eAAS,IAAI,SAAS,SAAS,GAAG,IAAI,IAAI,KAAK;AAC7C,cAAM,UAAU,SAAS,CAAC;AAC1B,YAAI,YAAY,oBAAoB,QAAQ,aAAa,YAAY,QAAQ,aAAa,WAAW,CAAC,QAAQ,aAAa,WAAW,GAAG;AACvI,eAAK,oBAAoB,IAAI,SAAS,QAAQ,aAAa,aAAa,CAAC;AACzE,kBAAQ,aAAa,eAAe,MAAM;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM,SAAS,KAAK;AACpB,WAAO,SAAS,OAAO,mBAAmB,IAAI,KAAK;AAAA,EACrD;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAQ;AAAA,EAC3C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,QAAO;AAAA,IAChB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,eAAe,OAAO,UAAU;AACvC,MAAI,IAAI,MAAM;AACd,SAAO,KAAK;AACV,aAAS,MAAM,CAAC,CAAC;AAAA,EACnB;AACF;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,cAAc,YAAY,kBAAkB;AAAA,IACrE,SAAS;AAAA;AAAA;AAAA,MAGT;AAAA,MAAc;AAAA,IAAkB;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,MAAM;AAAA,IAClB,SAAS;AAAA,MAAC;AAAA,MAAe;AAAA,MAAc;AAAA;AAAA;AAAA,MAGvC;AAAA,IAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,cAAc,YAAY,kBAAkB;AAAA,MACrE,SAAS;AAAA;AAAA;AAAA,QAGT;AAAA,QAAc;AAAA,MAAkB;AAAA,MAChC,WAAW,CAAC,MAAM;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC74BH,SAAS,0CAA0C,IAAI,KAAK;AAAC;AAC7D,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,aAAa;AAAA;AAAA,EAEb,cAAc;AAAA;AAAA,EAEd,gBAAgB;AAAA;AAAA,EAEhB,eAAe;AAAA;AAAA,EAEf,QAAQ;AAAA;AAAA,EAER,SAAS;AAAA;AAAA,EAET;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,eAAe;AAAA;AAAA,EAEf,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AACF;AAGA,IAAM,aAAa;AAEnB,IAAM,gBAAgB;AAEtB,IAAM,gBAAgB;AAEtB,IAAM,0BAA0B;AAEhC,IAAM,2BAA2B;AACjC,IAAM,qBAAN,MAAM,4BAA2B,mBAAmB;AAAA,EAClD,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,yBAAyB,IAAI,aAAa;AAAA;AAAA,EAE1C,qBAAqB,KAAK,mBAAmB;AAAA;AAAA,EAE7C,sBAAsB;AAAA;AAAA,EAEtB,eAAe,KAAK,YAAY;AAAA;AAAA,EAEhC,0BAA0B,KAAK,qBAAqB,aAAa,KAAK,QAAQ,sBAAsB,KAAK,0BAA0B;AAAA;AAAA,EAEnI,yBAAyB,KAAK,qBAAqB,aAAa,KAAK,QAAQ,qBAAqB,KAAK,2BAA2B;AAAA;AAAA,EAElI,kBAAkB;AAAA,EAClB,mBAAmB;AAGjB,UAAM,iBAAiB;AAOvB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,uBAAuB,KAAK;AAAA,MAC/B,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,QAAI,KAAK,oBAAoB;AAC3B,WAAK,aAAa,MAAM,YAAY,8BAA8B,GAAG,KAAK,uBAAuB,IAAI;AAIrG,WAAK,uBAAuB,MAAM,KAAK,aAAa,UAAU,IAAI,eAAe,UAAU,CAAC;AAC5F,WAAK,4BAA4B,KAAK,yBAAyB,KAAK,iBAAiB;AAAA,IACvF,OAAO;AACL,WAAK,aAAa,UAAU,IAAI,UAAU;AAK1C,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,kBAAkB,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,uBAAuB,KAAK;AAAA,MAC/B,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,SAAK,aAAa,UAAU,OAAO,UAAU;AAC7C,QAAI,KAAK,oBAAoB;AAC3B,WAAK,aAAa,MAAM,YAAY,8BAA8B,GAAG,KAAK,sBAAsB,IAAI;AAEpG,WAAK,uBAAuB,MAAM,KAAK,aAAa,UAAU,IAAI,aAAa,CAAC;AAChF,WAAK,4BAA4B,KAAK,wBAAwB,KAAK,kBAAkB;AAAA,IACvF,OAAO;AAkBL,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,mBAAmB,CAAC;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,OAAO;AAC/B,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,MAAM;AACxB,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB,KAAK,uBAAuB;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,MAAM;AACzB,SAAK,uBAAuB;AAC5B,SAAK,uBAAuB,KAAK;AAAA,MAC/B,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,yBAAyB;AACvB,SAAK,aAAa,UAAU,OAAO,eAAe,aAAa;AAAA,EACjE;AAAA,EACA,4BAA4B,UAAU,UAAU;AAC9C,QAAI,KAAK,oBAAoB,MAAM;AACjC,mBAAa,KAAK,eAAe;AAAA,IACnC;AAGA,SAAK,kBAAkB,WAAW,UAAU,QAAQ;AAAA,EACtD;AAAA;AAAA,EAEA,uBAAuB,UAAU;AAC/B,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,OAAO,0BAA0B,YAAY;AAC/C,8BAAsB,QAAQ;AAAA,MAChC,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,QAAI,CAAC,KAAK,QAAQ,gBAAgB;AAChC,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,WAAW;AAC5B,QAAI,KAAK,QAAQ,gBAAgB;AAC/B,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,uBAAuB,KAAK;AAAA,MAC/B,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,QAAI,KAAK,oBAAoB,MAAM;AACjC,mBAAa,KAAK,eAAe;AAAA,IACnC;AAAA,EACF;AAAA,EACA,sBAAsB,QAAQ;AAS5B,UAAM,MAAM,MAAM,sBAAsB,MAAM;AAC9C,QAAI,SAAS,cAAc,UAAU,IAAI,+BAA+B;AACxE,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,CAAC,YAAY,MAAM,GAAG,4BAA4B,YAAY;AAAA,IACzE,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,QAAQ,EAAE;AACtC,QAAG,YAAY,cAAc,IAAI,QAAQ,SAAS,EAAE,QAAQ,IAAI,QAAQ,IAAI,EAAE,mBAAmB,IAAI,QAAQ,YAAY,OAAO,IAAI,qBAAqB,CAAC,CAAC,EAAE,cAAc,IAAI,QAAQ,SAAS,EAAE,oBAAoB,IAAI,QAAQ,mBAAmB,IAAI;AACzP,QAAG,YAAY,2BAA2B,CAAC,IAAI,kBAAkB,EAAE,yCAAyC,IAAI,sBAAsB,CAAC;AAAA,MACzI;AAAA,IACF;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,uBAAuB,GAAG,CAAC,GAAG,0BAA0B,qBAAqB,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,IACtJ,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,CAAC;AAClF,QAAG,aAAa,EAAE;AAAA,MACpB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,QAAQ,CAAC,ulKAA2lK;AAAA,IACpmK,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,eAAe;AAAA,MACzB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,qBAAqB;AAAA,QACrB,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,0BAA0B;AAAA,QAC1B,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,QAC3B,mCAAmC;AAAA,QACnC,iDAAiD;AAAA,MACnD;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,ulKAA2lK;AAAA,IACtmK,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,+BAA+B;AAOrC,SAAS,aAAa,MAAM;AAC1B,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,KAAK,SAAS,IAAI,GAAG;AACvB,WAAO,qBAAqB,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,EAChE;AACA,MAAI,KAAK,SAAS,GAAG,GAAG;AACtB,WAAO,qBAAqB,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC,CAAC,IAAI;AAAA,EACpE;AACA,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI;AAAA,CACH,SAAUA,iBAAgB;AACzB,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,CAAC,IAAI;AAC7C,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,CAAC,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,CAAC,IAAI;AACjD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAI1C,IAAM,eAAN,MAAmB;AAAA,EACjB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,eAAe,IAAI,QAAQ;AAAA;AAAA,EAE3B,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,SAAS,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB;AAAA,EACA,YAAY,MAAM,QAAQ,oBAAoB;AAC5C,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAC1B,SAAK,eAAe,OAAO;AAC3B,SAAK,KAAK,KAAK;AAEf,SAAK,cAAc,sBAAsB;AAEzC,uBAAmB,uBAAuB,KAAK,OAAO,WAAS,MAAM,UAAU,QAAQ,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACjH,WAAK,aAAa,KAAK;AACvB,WAAK,aAAa,SAAS;AAAA,IAC7B,CAAC;AAED,uBAAmB,uBAAuB,KAAK,OAAO,WAAS,MAAM,UAAU,QAAQ,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACjH,mBAAa,KAAK,qBAAqB;AACvC,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AACD,SAAK,WAAW,YAAY,EAAE,UAAU,MAAM;AAC5C,WAAK,cAAc,KAAK,KAAK,OAAO;AACpC,WAAK,cAAc,SAAS;AAC5B,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AACD,UAAM,KAAK,cAAc,GAAG,KAAK,cAAc,EAAE,KAAK,OAAO,WAAS,MAAM,YAAY,UAAU,CAAC,KAAK,gBAAgB,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,WAAS;AACnK,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,eAAe;AACrB,wBAAgB,MAAM,MAAM,SAAS,YAAY,aAAa,OAAO;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc;AAClB,SAAK,UAAU;AAEf,SAAK,mBAAmB,uBAAuB,KAAK,OAAO,WAAS,MAAM,UAAU,SAAS,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC1H,WAAK,cAAc,KAAK,YAAY;AACpC,WAAK,cAAc,SAAS;AAC5B,WAAK,KAAK,WAAW,eAAe;AAMpC,WAAK,wBAAwB,WAAW,MAAM,KAAK,mBAAmB,GAAG,MAAM,YAAY,GAAG;AAAA,IAChG,CAAC;AACD,SAAK,SAAS,eAAe;AAC7B,SAAK,mBAAmB,oBAAoB;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,UAAU;AACvB,QAAI,WAAW,KAAK,KAAK,OAAO;AAChC,QAAI,aAAa,SAAS,QAAQ,SAAS,QAAQ;AACjD,eAAS,OAAO,SAAS,KAAK,SAAS,IAAI,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,IAC9E,OAAO;AACL,eAAS,mBAAmB;AAAA,IAC9B;AACA,QAAI,aAAa,SAAS,OAAO,SAAS,SAAS;AACjD,eAAS,MAAM,SAAS,IAAI,SAAS,GAAG,IAAI,SAAS,OAAO,SAAS,MAAM;AAAA,IAC7E,OAAO;AACL,eAAS,iBAAiB;AAAA,IAC5B;AACA,SAAK,KAAK,eAAe;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,QAAQ,IAAI,SAAS,IAAI;AAClC,SAAK,KAAK,WAAW,OAAO,MAAM;AAClC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,SAAS;AACrB,SAAK,KAAK,cAAc,OAAO;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,SAAK,KAAK,iBAAiB,OAAO;AAClC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,SAAK,SAAS,eAAe;AAC7B,SAAK,KAAK,MAAM,KAAK,SAAS;AAAA,MAC5B,aAAa,KAAK;AAAA,IACpB,CAAC;AACD,SAAK,oBAAoB;AAAA,EAC3B;AACF;AAOA,SAAS,gBAAgB,KAAK,iBAAiB,QAAQ;AACrD,MAAI,wBAAwB;AAC5B,SAAO,IAAI,MAAM,MAAM;AACzB;AAGA,IAAM,kBAAkB,IAAI,eAAe,kBAAkB;AAE7D,IAAM,6BAA6B,IAAI,eAAe,gCAAgC;AAEtF,IAAM,6BAA6B,IAAI,eAAe,kCAAkC;AAAA,EACtF,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,MAAM;AAAA,EAC9C;AACF,CAAC;AAMD,SAAS,4CAA4C,SAAS;AAC5D,SAAO,MAAM,QAAQ,iBAAiB,MAAM;AAC9C;AAMA,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAIA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,WAAW,OAAO,OAAO;AAAA,EACzB,kBAAkB,OAAO,4BAA4B;AAAA,IACnD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,kBAAkB,OAAO,0BAA0B;AAAA,EACnD,gBAAgB,OAAO,YAAW;AAAA,IAChC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,YAAY;AAAA,EAClC,UAAU,OAAO,MAAM;AAAA,EACvB,0BAA0B,CAAC;AAAA,EAC3B,6BAA6B,IAAI,QAAQ;AAAA,EACzC,0BAA0B,IAAI,QAAQ;AAAA,EACtC,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,cAAc,cAAc,KAAK;AAAA,EACpE;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,cAAc,cAAc,KAAK;AAAA,EACpE;AAAA,EACA,qBAAqB;AACnB,UAAM,SAAS,KAAK;AACpB,WAAO,SAAS,OAAO,mBAAmB,IAAI,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM,MAAM,KAAK,YAAY,SAAS,KAAK,mBAAmB,IAAI,KAAK,mBAAmB,EAAE,KAAK,UAAU,MAAS,CAAC,CAAC;AAAA,EACvI,cAAc;AACZ,SAAK,wBAAwB;AAC7B,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,KAAK,wBAAwB,QAAQ;AACnC,QAAI;AACJ,aAAS,kCACH,KAAK,mBAAmB,IAAI,gBAAgB,IAC7C;AAEL,WAAO,KAAK,OAAO,MAAM,KAAK,aAAa,MAAM,iBAAiB;AAClE,WAAO,iBAAiB,OAAO,kBAAkB,KAAK,gBAAgB;AACtE,UAAM,SAAS,KAAK,QAAQ,KAAK,wBAAwB,iCACpD,SADoD;AAAA,MAEvD,kBAAkB,KAAK,SAAS,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,iBAAiB;AAAA;AAAA,MAE1F,cAAc;AAAA;AAAA;AAAA;AAAA,MAId,gBAAgB;AAAA;AAAA;AAAA,MAGhB,2BAA2B;AAAA,MAC3B,WAAW;AAAA,QACT,MAAM,KAAK;AAAA,QACX,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA,UAIjB;AAAA,YACE,SAAS,KAAK;AAAA,YACd,UAAU;AAAA,UACZ;AAAA,UAAG;AAAA,YACD,SAAS;AAAA,YACT,UAAU;AAAA,UACZ;AAAA,QAAC;AAAA,MACH;AAAA,MACA,iBAAiB,OAAO;AAAA,QACtB;AAAA,MACF;AAAA,MACA,WAAW,CAAC,KAAK,WAAW,oBAAoB;AAC9C,oBAAY,IAAI,KAAK,sBAAsB,KAAK,QAAQ,eAAe;AACvE,kBAAU,eAAe,QAAQ,QAAQ;AACzC,eAAO,CAAC;AAAA,UACN,SAAS,KAAK;AAAA,UACd,UAAU;AAAA,QACZ,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,UACd,UAAU,UAAU;AAAA,QACtB,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,UACd,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF,EAAC;AAGD,cAAU,eAAe,OAAO;AAChC,cAAU,oBAAoB,OAAO;AACrC,SAAK,YAAY,KAAK,SAAS;AAC/B,SAAK,YAAY,KAAK,SAAS;AAC/B,cAAU,YAAY,EAAE,UAAU,MAAM;AACtC,YAAM,QAAQ,KAAK,YAAY,QAAQ,SAAS;AAChD,UAAI,QAAQ,IAAI;AACd,aAAK,YAAY,OAAO,OAAO,CAAC;AAChC,YAAI,CAAC,KAAK,YAAY,QAAQ;AAC5B,eAAK,mBAAmB,EAAE,KAAK;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,SAAK,cAAc,KAAK,WAAW;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,IAAI;AAChB,WAAO,KAAK,YAAY,KAAK,YAAU,OAAO,OAAO,EAAE;AAAA,EACzD;AAAA,EACA,cAAc;AAGZ,SAAK,cAAc,KAAK,uBAAuB;AAC/C,SAAK,2BAA2B,SAAS;AACzC,SAAK,wBAAwB,SAAS;AAAA,EACxC;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,IAAI,QAAQ;AAChB,WAAO,KAAK;AACV,cAAQ,CAAC,EAAE,MAAM;AAAA,IACnB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,WAAU;AAAA,IACnB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,OAAO,cAAc;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,SAAS;AAAA;AAAA,EAE1B;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,CAAC,KAAK,WAAW;AAMnB,WAAK,YAAY,iBAAiB,KAAK,aAAa,KAAK,QAAQ,WAAW;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,gBAAgB,QAAQ,iBAAiB,KAAK,QAAQ,uBAAuB;AACnF,QAAI,eAAe;AACjB,WAAK,eAAe,cAAc;AAAA,IACpC;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AAKpB,oBAAgB,KAAK,WAAW,MAAM,YAAY,KAAK,MAAM,YAAY,IAAI,aAAa,SAAS,KAAK,YAAY;AAAA,EACtH;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACpE,UAAU;AAAA,IACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,wCAAwC,QAAQ;AAC9E,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,IAAI,aAAa,IAAI,EAAE,QAAQ,IAAI,IAAI;AAAA,MACtE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,MAAM;AAAA,MACN,cAAc,CAAC,GAAG,oBAAoB,cAAc;AAAA,MACpD,iBAAiB,CAAC,GAAG,kBAAkB,iBAAiB;AAAA,IAC1D;AAAA,IACA,UAAU,CAAC,gBAAgB;AAAA,IAC3B,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,eAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,aAAa,OAAO,cAAc;AAAA,IAChC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,SAAS;AAAA,EAC1B,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,iBAAiB,KAAK,aAAa,KAAK,QAAQ,WAAW;AAAA,IAC/E;AACA,QAAI,KAAK,YAAY;AACnB,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,OAAO;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AAGZ,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,UAAU;AACZ,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,UAAU;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,EACR,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAIH,IAAM,iBAAN,MAAM,wBAAuB,uBAAuB;AAAA,EAClD,KAAK,OAAO,YAAY,EAAE,MAAM,uBAAuB;AAAA,EACvD,SAAS;AAGP,SAAK,WAAW,oBAAoB,qBAAqB,KAAK,EAAE;AAAA,EAClE;AAAA,EACA,YAAY;AACV,SAAK,YAAY,oBAAoB,wBAAwB,KAAK,EAAE;AAAA,EACtE;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACpE,WAAW,CAAC,GAAG,wBAAwB,mBAAmB;AAAA,IAC1D,UAAU;AAAA,IACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAAA,MAChC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,IAAI;AAAA,IACN;AAAA,IACA,UAAU,CAAC,gBAAgB;AAAA,IAC3B,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,GAAG,CAAC,oBAAoB,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IAChG,WAAW,CAAC,GAAG,0BAA0B,qBAAqB;AAAA,IAC9D,UAAU,CAAI,wBAAwB,CAAI,aAAa,CAAC,CAAC;AAAA,EAC3D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,gBAAgB,CAAC,aAAa;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,mBAAN,MAAM,0BAAyB,uBAAuB;AAAA;AAAA;AAAA;AAAA,EAIpD;AAAA,EACA,SAAS;AACP,SAAK,WAAW,oBAAoB,4BAA4B,CAAC;AAAA,EACnE;AAAA,EACA,YAAY;AACV,SAAK,WAAW,oBAAoB,4BAA4B,EAAE;AAAA,EACpE;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,GAAG,CAAC,oBAAoB,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IAChG,WAAW,CAAC,GAAG,0BAA0B,qBAAqB;AAAA,IAC9D,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,sCAAsC,IAAI,UAAU,OAAO,EAAE,uCAAuC,IAAI,UAAU,QAAQ,EAAE,oCAAoC,IAAI,UAAU,KAAK;AAAA,MACpM;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,8CAA8C;AAAA,QAC9C,+CAA+C;AAAA,QAC/C,4CAA4C;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,iBAAiB,SAAS,aAAa;AAC9C,MAAI,SAAS,QAAQ,cAAc;AACnC,SAAO,UAAU,CAAC,OAAO,UAAU,SAAS,0BAA0B,GAAG;AACvE,aAAS,OAAO;AAAA,EAClB;AACA,SAAO,SAAS,YAAY,KAAK,YAAU,OAAO,OAAO,OAAO,EAAE,IAAI;AACxE;AACA,IAAM,aAAa,CAAC,oBAAoB,gBAAgB,gBAAgB,kBAAkB,gBAAgB;AAC1G,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,eAAe,cAAc,iBAAiB,oBAAoB,gBAAgB,gBAAgB,kBAAkB,gBAAgB;AAAA,IAC5J,SAAS,CAAC,iBAAiB,oBAAoB,gBAAgB,gBAAgB,kBAAkB,gBAAgB;AAAA,EACnH,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,SAAS;AAAA,IACrB,SAAS,CAAC,cAAc,eAAe,cAAc,iBAAiB,eAAe;AAAA,EACvF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,eAAe,cAAc,iBAAiB,GAAG,UAAU;AAAA,MACnF,SAAS,CAAC,iBAAiB,GAAG,UAAU;AAAA,MACxC,WAAW,CAAC,SAAS;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,iBAAiB;AAAA,EACrB,QAAQ;AAAA,IACN,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,EACzB;AACF;AAOA,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6B1B,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,WAAW;AAAA,cACX,SAAS;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,UACA,SAAS;AAAA,QACX,GAAG;AAAA,UACD,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,QACD,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,UACN,wBAAwB;AAAA,UACxB,uBAAuB;AAAA,QACzB;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,UACA,SAAS;AAAA,QACX,GAAG;AAAA,UACD,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,QACD,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,UACN,wBAAwB;AAAA,UACxB,uBAAuB;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": ["MatDialogState"]}