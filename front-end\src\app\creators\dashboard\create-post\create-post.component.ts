import { Component, OnInit } from '@angular/core';
import { Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { CalendarProject } from '../calendar-view/calendar-view.models';



@Component({
  selector: 'app-create-post',
  templateUrl: './create-post.component.html',
  styleUrls: ['./create-post.component.css'],
  imports:[CommonModule, ReactiveFormsModule],
})
export class CreatePostComponent implements OnInit {
  postForm!: FormGroup;
  selectedImages: File[] = [];
  selectedVideos: File[] = [];
  assignedProjects: CalendarProject[] = [];

  constructor(
    private fb: FormBuilder,
    private postService: PostService,
    private router: Router,
    private creatorDashboardService: CreatorDashboardService
  ) {}

  ngOnInit(): void {
    this.postForm = this.fb.group({
      title: ['', Validators.required],
      description: ['', Validators.required],
      project: ['', Validators.required],
      scheduled_time: ['', Validators.required]
    });

    // Load assigned projects
    this.creatorDashboardService.getAssignedProjects().subscribe({
      next: (projects) => {
        this.assignedProjects = projects;
      },
      error: (error) => {
        console.error('Error loading projects:', error);
      }
    });
  }


  onImageSelected(event: any): void {
    this.selectedImages = Array.from(event.target.files);
  }

  onVideoSelected(event: any): void {
    this.selectedVideos = Array.from(event.target.files);
  }

  onSubmit(): void {
    if (this.postForm.invalid) {
      this.postForm.markAllAsTouched();
      return;
    }

    const formData = new FormData();
    formData.append('title', this.postForm.get('title')?.value);
    formData.append('description', this.postForm.get('description')?.value);
    formData.append('project', this.postForm.get('project')?.value);
    formData.append('scheduled_time', this.postForm.get('scheduled_time')?.value);

    // For now, just upload the first image or video as media (adjust backend for multiple)
    if (this.selectedImages.length > 0) {
      formData.append('media', this.selectedImages[0], this.selectedImages[0].name);
    } else if (this.selectedVideos.length > 0) {
      formData.append('media', this.selectedVideos[0], this.selectedVideos[0].name);
    }

    this.postService.createPostWithFiles(formData).subscribe({
      next: () => this.router.navigate(['/creators/dashboard/post-list']),
      error: (error) => {
        console.error('Error creating post:', error);
        alert('Failed to create post. Please try again.');
      }
    });
  }

}
