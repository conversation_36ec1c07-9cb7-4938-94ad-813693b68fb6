#!/usr/bin/env python
"""
Simple script to add test data to the database
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from core.models import User, Company, Project, Post
from django.db import transaction
from datetime import datetime, timedelta

def create_test_data():
    print('Starting database reset...')
    
    with transaction.atomic():
        # Clear all existing data
        print('Clearing existing data...')
        Post.objects.all().delete()
        Project.objects.all().delete()
        Company.objects.all().delete()
        User.objects.all().delete()
        
        # Create Super Admin
        print('Creating Super Admin...')
        super_admin = User.objects.create_user(
            username='superadmin',
            email='<EMAIL>',
            password='admin123',
            first_name='Super',
            last_name='Admin',
            role='super_admin',
            is_staff=True,
            is_superuser=True
        )
        
        # Create Companies
        print('Creating companies...')
        companies = []
        for name in ['TechCorp Solutions', 'Digital Marketing Pro', 'Creative Agency Ltd']:
            company = Company.objects.create(name=name)
            companies.append(company)
            print(f'  Created company: {company.name}')
        
        # Create Company Admins
        print('Creating Company Admins...')
        company_admins = []
        admin_data = [
            ('admin_techcorp', '<EMAIL>', 'John', 'Smith', companies[0]),
            ('admin_digital', '<EMAIL>', 'Sarah', 'Johnson', companies[1]),
            ('admin_creative', '<EMAIL>', 'Mike', 'Wilson', companies[2])
        ]
        
        for username, email, first_name, last_name, company in admin_data:
            admin = User.objects.create_user(
                username=username,
                email=email,
                password='admin123',
                first_name=first_name,
                last_name=last_name,
                role='company_admin',
                company=company
            )
            company_admins.append(admin)
            print(f'  Created admin: {admin.username} for {admin.company.name}')
        
        # Create Creators
        print('Creating Creators...')
        creators = []
        creator_data = [
            ('creator_alice', '<EMAIL>', 'Alice', 'Brown', companies[0]),
            ('creator_bob', '<EMAIL>', 'Bob', 'Davis', companies[0]),
            ('creator_carol', '<EMAIL>', 'Carol', 'Miller', companies[1]),
            ('creator_david', '<EMAIL>', 'David', 'Garcia', companies[1]),
            ('creator_eve', '<EMAIL>', 'Eve', 'Martinez', companies[2])
        ]
        
        for username, email, first_name, last_name, company in creator_data:
            creator = User.objects.create_user(
                username=username,
                email=email,
                password='creator123',
                first_name=first_name,
                last_name=last_name,
                role='creator',
                company=company
            )
            creators.append(creator)
            print(f'  Created creator: {creator.username} for {creator.company.name}')
        
        # Create Projects
        print('Creating Projects...')
        projects_data = [
            ('Q1_Social_Campaign', 'Q1 Social Media Campaign', 'Social media content for Q1 product launch', 30, companies[0], company_admins[0], [creators[0], creators[1]]),
            ('Tech_Blog_Series', 'Technical Blog Series', 'Weekly technical blog posts', 45, companies[0], company_admins[0], [creators[0]]),
            ('Client_Content_Pack', 'Client Content Package', 'Monthly content package for key client', 20, companies[1], company_admins[1], [creators[2], creators[3]]),
            ('Brand_Refresh', 'Brand Refresh Campaign', 'Complete brand refresh for major client', 60, companies[2], company_admins[2], [creators[4]])
        ]
        
        for name, title, description, days, company, admin, project_creators in projects_data:
            project = Project.objects.create(
                name=name,
                title=title,
                description=description,
                deadline=datetime.now() + timedelta(days=days),
                company=company,
                created_by=admin
            )
            project.creators.set(project_creators)
            print(f'  Created project: {project.title} for {project.company.name}')
            print(f'    Assigned creators: {[c.username for c in project_creators]}')

    print('Database reset completed successfully!')
    print('Test accounts created:')
    print('- Super Admin: superadmin / admin123')
    print('- Company Admins: admin_techcorp, admin_digital, admin_creative / admin123')
    print('- Creators: creator_alice, creator_bob, creator_carol, creator_david, creator_eve / creator123')
    print('Each role has proper company assignments and project relationships.')

if __name__ == '__main__':
    create_test_data()
