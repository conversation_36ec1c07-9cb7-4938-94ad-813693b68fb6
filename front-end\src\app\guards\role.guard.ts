import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    const token = localStorage.getItem('access_token');
    if (!token) {
      this.router.navigate(['/login']);
      return false;
    }

    const userRole = this.getRoleFromToken(token);
    const requiredRoles = route.data['roles'] as Array<string>;

    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No role restriction
    }

    if (requiredRoles.includes(userRole)) {
      return true;
    }

    // Redirect based on user role
    this.redirectToAuthorizedRoute(userRole);
    return false;
  }

  private getRoleFromToken(token: string): string {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || '';
    } catch (e) {
      return '';
    }
  }

  private redirectToAuthorizedRoute(role: string): void {
    switch (role) {
      case 'super_admin':
        this.router.navigate(['/super-admin']);
        break;
      case 'company_admin':
        this.router.navigate(['/company-admin']);
        break;
      case 'creator':
        this.router.navigate(['/creators']);
        break;
      default:
        this.router.navigate(['/login']);
        break;
    }
  }
}

@Injectable({
  providedIn: 'root'
})
export class PermissionService {
  
  constructor() {}

  getUserRole(): string {
    const token = localStorage.getItem('access_token');
    if (!token) return '';
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || '';
    } catch (e) {
      return '';
    }
  }

  hasPermission(action: string): boolean {
    const role = this.getUserRole();
    
    const permissions = {
      'super_admin': [
        'create_company',
        'create_project',
        'set_project_title_deadline',
        'assign_creator',
        'view_all_data'
      ],
      'company_admin': [
        'create_project',
        'set_project_title_deadline',
        'review_post',
        'assign_creator',
        'view_company_data'
      ],
      'creator': [
        'post_creation',
        'submit_for_review',
        'view_assigned_projects'
      ]
    };

    return permissions[role as keyof typeof permissions]?.includes(action) || false;
  }

  canCreateCompany(): boolean {
    return this.hasPermission('create_company');
  }

  canCreateProject(): boolean {
    return this.hasPermission('create_project');
  }

  canSetProjectDetails(): boolean {
    return this.hasPermission('set_project_title_deadline');
  }

  canReviewPost(): boolean {
    return this.hasPermission('review_post');
  }

  canCreatePost(): boolean {
    return this.hasPermission('post_creation');
  }

  canSubmitForReview(): boolean {
    return this.hasPermission('submit_for_review');
  }

  canAssignCreator(): boolean {
    return this.hasPermission('assign_creator');
  }

  isCreator(): boolean {
    return this.getUserRole() === 'creator';
  }

  isCompanyAdmin(): boolean {
    return this.getUserRole() === 'company_admin';
  }

  isSuperAdmin(): boolean {
    return this.getUserRole() === 'super_admin';
  }
}
