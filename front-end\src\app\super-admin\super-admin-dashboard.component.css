/* Dashboard Header */
.dashboard-header {
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.spacer {
  flex: 1 1 auto;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  text-align: center;
}

.loading-section p {
  margin-top: 12px;
  color: #666;
  font-size: 14px;
}

/* Main Dashboard */
.dashboard-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* Welcome Card */
.welcome-card {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.welcome-card mat-card-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 16px;
}

.stat-card mat-card-content {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-number.active {
  color: #4caf50;
}

.stat-number.assigned {
  color: #2196f3;
}

.stat-number.pending {
  color: #ff9800;
  position: relative;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Quick Actions */
.quick-actions-card {
  margin-bottom: 24px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  height: auto;
  text-align: left;
  justify-content: flex-start;
}

.action-content {
  display: flex;
  flex-direction: column;
}

.action-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.action-description {
  font-size: 12px;
  opacity: 0.8;
}

/* Content Section */
.content-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

/* Company Management Panel */
.companies-panel mat-card-header {
  display: flex;
  align-items: center;
}

.companies-panel mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.company-item {
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 0;
}

.company-item:last-child {
  border-bottom: none;
}

.company-item mat-chip-set {
  margin-top: 8px;
}

.company-item mat-chip {
  font-size: 11px;
  height: 24px;
}

/* Activity Panel */
.activity-item {
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 0;
}

.activity-item:last-child {
  border-bottom: none;
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.empty-state mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.empty-state p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .stat-card mat-card-content {
    flex-direction: column;
    gap: 12px;
  }
}

/* Material Design Overrides */
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.mat-mdc-card-header {
  padding-bottom: 8px !important;
}

/* Snackbar Styles */
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

/* Expansion Panel Styles */
.company-accordion {
  width: 100%;
}

.company-panel {
  margin-bottom: 8px !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.company-panel .mat-expansion-panel-header {
  padding: 16px 24px;
  height: auto !important;
  min-height: 64px;
}

.company-panel .mat-panel-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  font-size: 16px;
}

.company-name {
  color: #1976d2;
  font-weight: 600;
}

.company-panel .mat-panel-description {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin-left: 16px;
}

.settings-button {
  margin-left: 8px;
}

/* Projects Content Styles */
.projects-content {
  padding: 16px 24px;
  background-color: #fafafa;
}

.loading-projects {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  text-align: center;
}

.loading-projects p {
  margin-top: 16px;
  color: #666;
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.project-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-item:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
  border-color: #1976d2;
  transform: translateY(-1px);
}

.project-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.project-header mat-icon {
  color: #ff9800;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.project-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.project-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.deadline, .creators-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.deadline mat-icon, .creators-count mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.deadline {
  color: #f44336;
  font-weight: 500;
}

.no-projects {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  text-align: center;
  color: #666;
}

.no-projects mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.add-project-button {
  width: 100%;
  margin-top: 8px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

/* Company Details Content */
.company-details-content {
  padding: 16px 0;
}

.company-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.company-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-content {
  padding: 16px;
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.2s ease;
}

.user-item:hover {
  background: #f0f0f0;
  border-color: #2196f3;
}

.admin-item {
  border-left: 4px solid #ff9800;
}

.creator-item {
  border-left: 4px solid #4caf50;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.admin-item .user-avatar {
  background: #fff3e0;
  color: #ff9800;
}

.creator-item .user-avatar {
  background: #e8f5e8;
  color: #4caf50;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.user-details {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
}

.username {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.email {
  font-size: 12px;
  color: #888;
}

.user-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #999;
}

.projects-count {
  color: #4caf50;
  font-weight: 500;
}

.join-date {
  color: #666;
}

.user-actions {
  display: flex;
  gap: 4px;
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #666;
}

.empty-state mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.section-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

/* Company Stats */
.company-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #2196f3;
}
