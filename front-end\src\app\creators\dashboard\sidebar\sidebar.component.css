.sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 72px;
}

/* Header Section */
.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.logo-section {
  flex: 1;
}

.app-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: white;
}

.app-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.toggle-btn {
  color: white !important;
  background: rgba(255, 255, 255, 0.1);
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Create Post Section */
.create-post-section {
  padding: 20px;
}

.create-post-btn {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  text-transform: none;
  background: #10b981 !important;
  color: white !important;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.create-post-btn:hover {
  background: #059669 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.create-post-btn.icon-only {
  width: 48px;
  border-radius: 50%;
  justify-content: center;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 0 12px;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-button {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8) !important;
  text-transform: none;
  font-weight: 500;
  transition: all 0.3s ease;
  justify-content: flex-start;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
}

.nav-item.active .nav-button {
  background: rgba(255, 255, 255, 0.15);
  color: white !important;
  font-weight: 600;
}

.nav-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.nav-label {
  flex: 1;
  text-align: left;
}

.sidebar.collapsed .nav-button {
  justify-content: center;
  padding: 0;
}

/* Spacer */
.sidebar-spacer {
  flex: 1;
}

/* User Profile */
.user-profile {
  padding: 0;
}

.profile-content {
  padding: 20px;
}

.profile-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  color: white !important;
  text-transform: none;
  transition: all 0.3s ease;
}

.profile-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.profile-button.collapsed-profile {
  justify-content: center;
  padding: 12px;
}

.avatar-container {
  flex-shrink: 0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: white;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
}

.user-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

.profile-menu-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* User Menu */
.user-menu {
  margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .sidebar.collapsed {
    width: 280px;
  }
}

/* Badge Styling */
::ng-deep .mat-badge-content {
  background: #ef4444 !important;
  color: white !important;
  font-size: 10px !important;
  font-weight: 600 !important;
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
