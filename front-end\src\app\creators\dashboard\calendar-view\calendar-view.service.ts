import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest, map } from 'rxjs';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarDay, CalendarPost, CalendarFilter, PostStatus, StatusConfig, CalendarEvent, CalendarProject } from './calendar-view.models';
import { Post } from '../../models/post.model';

@Injectable({
  providedIn: 'root'
})
export class CalendarViewService {
  private currentDateSubject = new BehaviorSubject<Date>(new Date());
  private selectedDateSubject = new BehaviorSubject<Date | null>(null);
  private filterSubject = new BehaviorSubject<CalendarFilter>({
    statuses: ['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled'],
    searchTerm: '',
    tags: [],
    projectIds: [],
    companyIds: []
  });

  currentDate$ = this.currentDateSubject.asObservable();
  selectedDate$ = this.selectedDateSubject.asObservable();
  filter$ = this.filterSubject.asObservable();

  constructor(
    private postService: PostService,
    private creatorDashboardService: CreatorDashboardService
  ) {}

  // Status configuration matching existing color scheme
  getStatusConfig(status: PostStatus): StatusConfig {
    const configs: Record<PostStatus, StatusConfig> = {
      'posted': { color: '#4caf50', icon: 'check_circle', label: 'Posted' },
      'scheduled': { color: '#2196f3', icon: 'schedule', label: 'Scheduled' },
      'rework': { color: '#9c27b0', icon: 'edit', label: 'Rework' },
      'draft': { color: '#9e9e9e', icon: 'draft', label: 'Draft' },
      'submitted': { color: '#ff9800', icon: 'send', label: 'Submitted' },
      'rejected': { color: '#f44336', icon: 'cancel', label: 'Rejected' }
    };
    return configs[status];
  }

  // Date navigation methods
  navigateToMonth(date: Date): void {
    this.currentDateSubject.next(new Date(date));
  }

  navigateToPreviousMonth(): void {
    const current = this.currentDateSubject.value;
    const previous = new Date(current.getFullYear(), current.getMonth() - 1, 1);
    this.currentDateSubject.next(previous);
  }

  navigateToNextMonth(): void {
    const current = this.currentDateSubject.value;
    const next = new Date(current.getFullYear(), current.getMonth() + 1, 1);
    this.currentDateSubject.next(next);
  }

  navigateToToday(): void {
    this.currentDateSubject.next(new Date());
  }

  // Date selection
  selectDate(date: Date | null): void {
    this.selectedDateSubject.next(date);
  }

  // Filter methods
  updateFilter(filter: Partial<CalendarFilter>): void {
    const currentFilter = this.filterSubject.value;
    this.filterSubject.next({ ...currentFilter, ...filter });
  }

  // Generate calendar days for month view using creator dashboard data
  generateCalendarDays(date: Date): Observable<CalendarDay[]> {
    return combineLatest([
      this.creatorDashboardService.getCalendarData(),
      this.filter$
    ]).pipe(
      map(([dashboardData, filter]) => {
        const year = date.getFullYear();
        const month = date.getMonth();

        // Get first day of month and calculate start of calendar grid
        const firstDayOfMonth = new Date(year, month, 1);
        const startOfCalendar = new Date(firstDayOfMonth);
        startOfCalendar.setDate(startOfCalendar.getDate() - firstDayOfMonth.getDay());

        // Generate 42 days (6 weeks)
        const days: CalendarDay[] = [];
        const today = new Date();
        const selectedDate = this.selectedDateSubject.value;
        
        for (let i = 0; i < 42; i++) {
          const currentDate = new Date(startOfCalendar);
          currentDate.setDate(startOfCalendar.getDate() + i);
          
          // Filter events for this day
          const dayPosts = this.filterEventsForDay(dashboardData.events, currentDate, filter, 'post');
          const dayProjectEvents = this.filterProjectEventsForDay(dashboardData.events, currentDate, filter);

          days.push({
            date: new Date(currentDate),
            isCurrentMonth: currentDate.getMonth() === month,
            isToday: this.isSameDay(currentDate, today),
            isSelected: selectedDate ? this.isSameDay(currentDate, selectedDate) : false,
            posts: dayPosts,
            projects: dayProjectEvents.map(event => this.convertEventToProject(event, dashboardData.projects))
          });
        }
        
        return days;
      })
    );
  }

  private filterEventsForDay(events: CalendarEvent[], date: Date, filter: CalendarFilter, type: 'post' | 'project'): CalendarPost[] {
    return events
      .filter(event => {
        // Filter by type
        if (event.type !== type) return false;

        // Filter by date
        const eventDate = new Date(event.date);
        if (!this.isSameDay(eventDate, date)) return false;

        // Filter by status (for posts)
        if (type === 'post' && event.status && !filter.statuses.includes(event.status)) return false;

        // Filter by search term
        if (filter.searchTerm && !event.title.toLowerCase().includes(filter.searchTerm.toLowerCase())) {
          return false;
        }

        // Filter by project IDs
        if (filter.projectIds.length > 0 && !filter.projectIds.includes(event.projectId)) {
          return false;
        }

        // Filter by company IDs
        if (filter.companyIds.length > 0 && !filter.companyIds.includes(event.companyId)) {
          return false;
        }

        return true;
      })
      .map(event => this.convertEventToCalendarPost(event));
  }

  private filterProjectEventsForDay(events: CalendarEvent[], date: Date, filter: CalendarFilter): CalendarEvent[] {
    return events
      .filter(event => {
        // Filter by type
        if (event.type !== 'project') return false;

        // Filter by deadline date
        if (!event.deadline) return false;
        const deadlineDate = new Date(event.deadline);
        if (!this.isSameDay(deadlineDate, date)) return false;

        // Filter by search term
        if (filter.searchTerm && !event.title.toLowerCase().includes(filter.searchTerm.toLowerCase())) {
          return false;
        }

        // Filter by project IDs
        if (filter.projectIds.length > 0 && !filter.projectIds.includes(event.projectId)) {
          return false;
        }

        // Filter by company IDs
        if (filter.companyIds.length > 0 && !filter.companyIds.includes(event.companyId)) {
          return false;
        }

        return true;
      });
  }

  private convertEventToProject(event: CalendarEvent, projects: CalendarProject[]): CalendarProject {
    const project = projects.find(p => p.id === event.projectId);
    return project || {
      id: event.projectId,
      title: event.title,
      name: event.title,
      companyName: event.companyName,
      companyId: event.companyId,
      deadline: event.deadline,
      postsCount: 0,
      pendingPostsCount: 0,
      company: {
        id: event.companyId,
        name: event.companyName
      }
    };
  }

  private convertEventToCalendarPost(event: CalendarEvent): CalendarPost {
    const scheduledDate = new Date(event.date);
    return {
      id: event.postId || parseInt(event.id.replace('post_', '')) || 0,
      title: event.title,
      description: event.description || '',
      time: scheduledDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      status: event.status || 'draft',
      mediaType: event.mediaType || event.media_type || null,
      thumbnailUrl: event.thumbnailUrl || event.thumbnail_url,
      scheduledDate,
      projectTitle: event.projectTitle || event.project_title || event.title,
      companyName: event.companyName || event.company_name || 'Unknown Company',
      projectId: event.projectId || event.project_id || 0,
      companyId: event.companyId || event.company_id || 0,
      deadline: event.deadline
    };
  }

  private convertToCalendarPost(post: Post): CalendarPost {
    const scheduledDate = new Date(post.scheduled_date || '');
    return {
      id: post.id || 0,
      title: post.title,
      description: post.description,
      time: scheduledDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      status: post.status,
      mediaType: this.getMediaType(post.media_url),
      thumbnailUrl: post.media_url,
      scheduledDate,
      projectTitle: '',
      companyName: '',
      projectId: 0,
      companyId: 0
    };
  }

  private getMediaType(mediaUrl: string): 'image' | 'video' | 'file' | null {
    if (!mediaUrl) return null;
    if (mediaUrl.includes('image') || /\.(jpg|jpeg|png|gif|webp)$/i.test(mediaUrl)) {
      return 'image';
    }
    if (mediaUrl.includes('video') || /\.(mp4|webm|ogg|avi|mov)$/i.test(mediaUrl)) {
      return 'video';
    }
    return 'file';
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  // Utility methods
  formatMonthYear(date: Date): string {
    return date.toLocaleDateString('en-US', { 
      month: 'long', 
      year: 'numeric' 
    });
  }

  getWeekDays(): string[] {
    return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  }
}
