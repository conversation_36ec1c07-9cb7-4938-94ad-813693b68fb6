import { Component, OnInit } from '@angular/core';
import { Post } from '../../models/post.model';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';



@Component({
  selector: 'app-post-list',
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    MatCardModule
  ],
  templateUrl: './post-list.component.html',
  styleUrl: './post-list.component.css'
})
export class PostListComponent implements OnInit {
  posts: Post[] = [];

  constructor(
    private postService: PostService,
    private creatorDashboardService: CreatorDashboardService
  ) {}

  ngOnInit(): void {
    this.fetchPosts();
  }

  fetchPosts(): void {
    // Use creator dashboard service to get filtered posts
    this.creatorDashboardService.getMyPosts().subscribe({
      next: (data) => this.posts = data,
      error: (error) => {
        console.error('Failed to fetch posts:', error);
        alert('Failed to fetch posts.');
      }
    });
  }

  // Media type checking
  isImage(fileUrl: string): boolean {
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(fileUrl);
  }

  isVideo(fileUrl: string): boolean {
    return /\.(mp4|webm|ogg)$/i.test(fileUrl);
  }

  // Status management
  getStatusColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      'draft': '#9e9e9e',
      'submitted': '#ff9800',
      'posted': '#4caf50',
      'rejected': '#f44336',
      'rework': '#ff5722',
      'scheduled': '#2196f3'
    };
    return statusColors[status] || '#9e9e9e';
  }

  getStatusTextColor(status: string): string {
    return '#ffffff';
  }

  getStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      'draft': 'Draft',
      'submitted': 'Submitted',
      'posted': 'Posted',
      'rejected': 'Rejected',
      'rework': 'Rework',
      'scheduled': 'Scheduled'
    };
    return statusLabels[status] || status;
  }

  // Project and company information
  getCompanyName(post: Post): string {
    // Extract company name from post detail if available
    return (post as any).company_name || 'Unknown Company';
  }

  getProjectDeadline(post: Post): Date | null {
    // Extract project deadline if available
    const deadline = (post as any).project_deadline;
    return deadline ? new Date(deadline) : null;
  }

  // File handling
  getFileName(url: string): string {
    return url.split('/').pop() || 'Unknown File';
  }

  // Action handlers
  openMediaPreview(post: Post): void {
    console.log('Opening media preview for:', post);
    // TODO: Implement media preview modal
  }

  editPost(post: Post): void {
    console.log('Editing post:', post);
    // TODO: Navigate to edit post page
  }

  viewPost(post: Post): void {
    console.log('Viewing post:', post);
    // TODO: Navigate to post detail page
  }

  deletePost(post: Post): void {
    if (confirm('Are you sure you want to delete this post?')) {
      this.postService.deletePost(post.id!).subscribe({
        next: () => {
          this.posts = this.posts.filter(p => p.id !== post.id);
          alert('Post deleted successfully.');
        },
        error: (error) => {
          console.error('Error deleting post:', error);
          alert('Failed to delete post.');
        }
      });
    }
  }

  submitForReview(post: Post): void {
    if (confirm('Are you sure you want to submit this post for review?')) {
      this.postService.submitPostForReview(post.id!).subscribe({
        next: (response) => {
          // Update the post status in the local array
          const index = this.posts.findIndex(p => p.id === post.id);
          if (index !== -1) {
            this.posts[index].status = 'submitted';
          }
          alert('Post submitted for review successfully.');
        },
        error: (error) => {
          console.error('Error submitting post for review:', error);
          alert('Failed to submit post for review.');
        }
      });
    }
  }
}
