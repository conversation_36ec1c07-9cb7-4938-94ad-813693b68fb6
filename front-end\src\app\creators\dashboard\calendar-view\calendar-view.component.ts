import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FullCalendarWrapperModule } from '../../../shared/full-calendar-wrapper.module';
import dayGridPlugin from '@fullcalendar/daygrid';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarEvent } from './calendar-view.models';

@Component({
  selector: 'app-calendar-view',
  standalone: true,
  imports: [CommonModule, FullCalendarWrapperModule],
  templateUrl: './calendar-view.component.html',
  styleUrls: ['./calendar-view.component.css']
})
export class CalendarViewComponent implements OnInit {
  calendarPlugins = [dayGridPlugin];
  calendarEvents: any[] = [];

  constructor(private creatorDashboardService: CreatorDashboardService) {}

  ngOnInit(): void {
    this.loadCalendarData();
  }

  private loadCalendarData(): void {
    this.creatorDashboardService.getCalendarData().subscribe({
      next: (data) => {
        // Convert calendar events to FullCalendar format
        this.calendarEvents = data.events.map(event => ({
          title: event.title,
          date: event.date.split('T')[0], // Extract date part only
          backgroundColor: this.getEventColor(event.type, event.status),
          borderColor: this.getEventColor(event.type, event.status),
          extendedProps: {
            type: event.type,
            status: event.status,
            projectName: event.projectName,
            companyName: event.companyName
          }
        }));
      },
      error: (error) => {
        console.error('Error loading calendar data:', error);
      }
    });
  }

  private getEventColor(type: string, status?: string): string {
    if (type === 'post') {
      switch (status) {
        case 'draft': return '#9e9e9e';
        case 'submitted': return '#ff9800';
        case 'posted': return '#4caf50';
        case 'rejected': return '#f44336';
        case 'rework': return '#ff5722';
        default: return '#2196f3';
      }
    } else if (type === 'project') {
      return '#673ab7';
    }
    return '#2196f3';
  }
}
