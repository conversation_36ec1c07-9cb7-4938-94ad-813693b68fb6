#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from core.models import User, Project, Post
from datetime import datetime
from django.core.files.base import ContentFile

def create_test_post():
    print("=== CREATING TEST POST FOR CLAIRE ===")
    
    # Get Claire
    try:
        claire = User.objects.get(username='claire')
        print(f"Found Claire: {claire.username}")
    except User.DoesNotExist:
        print("Claire not found!")
        return
    
    # Get her assigned projects
    assigned_projects = claire.assigned_projects.all()
    print(f"Claire's assigned projects: {[p.name for p in assigned_projects]}")
    
    if not assigned_projects.exists():
        print("No assigned projects found!")
        return
    
    project = assigned_projects.first()
    print(f"Using project: {project.name}")
    
    # Create a test file
    test_content = b"fake image content for testing"
    test_file = ContentFile(test_content, name="test_image.jpg")
    
    # Create test post
    now = datetime.now()
    post = Post.objects.create(
        title='Test Post by Claire',
        description='This is a test post created to verify the system works properly',
        project=project,
        creator=claire,
        scheduled_time=now,
        scheduled_date=now,
        media=test_file,
        status='draft'
    )
    
    print(f"✅ Created test post: {post.title} (ID: {post.id})")
    print(f"   Project: {post.project.name}")
    print(f"   Status: {post.status}")
    print(f"   Scheduled: {post.scheduled_time}")
    
    # Verify it shows up in queries
    posts_check = Post.objects.filter(
        creator=claire,
        project__in=assigned_projects
    )
    print(f"\nPosts now visible to Claire: {posts_check.count()}")
    
    for p in posts_check:
        print(f"  - {p.title} (Project: {p.project.name}, Status: {p.status})")

if __name__ == "__main__":
    create_test_post()
