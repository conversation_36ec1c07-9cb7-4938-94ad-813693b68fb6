# Generated by Django 5.2 on 2025-06-04 10:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0004_alter_post_media"),
    ]

    operations = [
        migrations.AddField(
            model_name="project",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="project",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_projects",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="deadline",
            field=models.DateTimeField(
                blank=True, help_text="Project deadline", null=True
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="description",
            field=models.TextField(
                blank=True, help_text="Project description", null=True
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="Project title for display",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
