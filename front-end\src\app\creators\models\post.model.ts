export interface Post {
  id?: number;
  title: string;
  description: string;                    // corresponds to `description` in Django
  media_url: string;                         // URL to image/video file from Django
  scheduled_date?: string;               // should match Django field
  scheduled_time?: string;               // if used on frontend
  status: 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled';
  creator?: number | string;             // ID or username
  project?: number;                      // project ID (for upload)

  // Additional fields from backend serializer
  project_name?: string;                 // Project name from backend
  creator_name?: string;                 // Creator name from backend
  company_name?: string;                 // Company name from project
  project_deadline?: string;             // Project deadline
  project_detail?: {                     // Full project details
    id: number;
    name: string;
    company_detail: {
      id: number;
      name: string;
      logo?: string;
    };
  };
}
