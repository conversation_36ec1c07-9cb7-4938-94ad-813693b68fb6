from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

class Command(BaseCommand):
    help = 'Fix authentication issues by checking and fixing user data'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting authentication fix...'))
        
        # Check all users
        users = User.objects.all()
        self.stdout.write(f'Found {users.count()} users in database')
        
        fixed_count = 0
        
        for user in users:
            issues = []
            
            # Check for empty role
            if not user.role or user.role.strip() == '':
                issues.append('empty_role')
            
            # Check for invalid role
            valid_roles = ['super_admin', 'company_admin', 'creator']
            if user.role and user.role not in valid_roles:
                issues.append('invalid_role')
            
            # Check for missing password
            if not user.password:
                issues.append('no_password')
            
            # Check if user is active
            if not user.is_active:
                issues.append('inactive')
            
            if issues:
                self.stdout.write(
                    self.style.WARNING(
                        f'User {user.username} has issues: {", ".join(issues)}'
                    )
                )
                
                # Fix empty role - set to creator by default
                if 'empty_role' in issues:
                    user.role = 'creator'
                    self.stdout.write(f'  - Set role to "creator" for {user.username}')
                
                # Fix invalid role
                if 'invalid_role' in issues:
                    user.role = 'creator'
                    self.stdout.write(f'  - Fixed invalid role for {user.username}')
                
                # Set password if missing (use username as default)
                if 'no_password' in issues:
                    user.set_password(user.username)
                    self.stdout.write(f'  - Set default password for {user.username}')
                
                # Activate user
                if 'inactive' in issues:
                    user.is_active = True
                    self.stdout.write(f'  - Activated user {user.username}')
                
                user.save()
                fixed_count += 1
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'User {user.username} is OK (role: {user.role})')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Fixed {fixed_count} users with authentication issues')
        )
        
        # Show final user status
        self.stdout.write('\nFinal user status:')
        for user in User.objects.all():
            self.stdout.write(
                f'  {user.username}: role={user.role}, active={user.is_active}, has_password={bool(user.password)}'
            )
