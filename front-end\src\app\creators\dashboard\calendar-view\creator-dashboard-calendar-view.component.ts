import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule as MatFabModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CalendarViewService } from './calendar-view.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarDay, CalendarPost, CalendarProject, PostStatus, StatusConfig } from './calendar-view.models';
import { PostDialogComponent } from '../post-dialog/post-dialog.component';

@Component({
  selector: 'app-creator-dashboard-calendar-view',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatFabModule,
    MatDialogModule,
    FormsModule
  ],
  templateUrl: './creator-dashboard-calendar-view.component.html',
  styleUrls: ['./creator-dashboard-calendar-view.component.css']
})
export class CreatorDashboardCalendarViewComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  calendarDays$: Observable<CalendarDay[]>;
  currentDate$: Observable<Date>;
  selectedDate$: Observable<Date | null>;
  
  searchTerm = '';
  selectedStatuses: PostStatus[] = ['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled'];
  
  readonly weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  readonly statusOptions: PostStatus[] = ['posted', 'scheduled', 'rework', 'draft', 'submitted', 'rejected'];

  constructor(
    private calendarService: CalendarViewService,
    private creatorDashboardService: CreatorDashboardService,
    private dialog: MatDialog
  ) {
    this.calendarDays$ = new Observable<CalendarDay[]>();
    this.currentDate$ = this.calendarService.currentDate$;
    this.selectedDate$ = this.calendarService.selectedDate$;
  }

  ngOnInit(): void {
    // Load creator dashboard data first
    this.creatorDashboardService.refreshDashboardData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          console.log('Dashboard data loaded:', data);
          // Refresh calendar after data is loaded
          this.refreshCalendarData();
        },
        error: (error) => {
          console.error('Error loading dashboard data:', error);
        }
      });

    // Initialize calendar days observable
    this.currentDate$
      .pipe(takeUntil(this.destroy$))
      .subscribe(date => {
        this.calendarDays$ = this.calendarService.generateCalendarDays(date);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Navigation methods
  navigateToPreviousMonth(): void {
    this.calendarService.navigateToPreviousMonth();
  }

  navigateToNextMonth(): void {
    this.calendarService.navigateToNextMonth();
  }

  navigateToToday(): void {
    this.calendarService.navigateToToday();
  }

  onDatePickerChange(date: Date): void {
    this.calendarService.navigateToMonth(date);
  }

  // Day selection
  onDayClick(day: CalendarDay): void {
    this.calendarService.selectDate(day.date);
    
    // If day has no posts, open create post dialog
    if (day.posts.length === 0) {
      this.openCreatePostDialog(day.date);
    }
  }

  onPostClick(post: CalendarPost, event: Event): void {
    event.stopPropagation();
    // Handle post click - could open post detail or edit dialog
    console.log('Post clicked:', post);
  }

  onThumbnailClick(post: CalendarPost, event: Event): void {
    event.stopPropagation();
    // Open thumbnail preview modal or navigate to content detail
    console.log('Thumbnail clicked:', post);
    // TODO: Implement thumbnail preview modal
  }

  // Post creation
  openCreatePostDialog(date?: Date): void {
    let selectedDate = date || new Date();

    // Get current selected date from service if no date provided
    if (!date) {
      this.calendarService.selectedDate$.pipe(takeUntil(this.destroy$)).subscribe(currentSelected => {
        if (currentSelected) {
          selectedDate = currentSelected;
        }
      });
    }

    // Get assigned projects for the dialog
    this.creatorDashboardService.assignedProjects$.pipe(takeUntil(this.destroy$)).subscribe(projects => {
      const dialogRef = this.dialog.open(PostDialogComponent, {
        width: '600px',
        data: {
          date: selectedDate.toISOString().split('T')[0],
          assignedProjects: projects
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Refresh calendar data
          this.refreshCalendarData();
        }
      });
    });
  }

  // Filtering
  onSearchChange(): void {
    this.calendarService.updateFilter({ searchTerm: this.searchTerm });
  }

  onStatusFilterChange(status: PostStatus): void {
    const index = this.selectedStatuses.indexOf(status);
    if (index > -1) {
      this.selectedStatuses.splice(index, 1);
    } else {
      this.selectedStatuses.push(status);
    }
    this.calendarService.updateFilter({ statuses: [...this.selectedStatuses] });
  }

  isStatusSelected(status: PostStatus): boolean {
    return this.selectedStatuses.includes(status);
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatuses = ['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled'];
    this.calendarService.updateFilter({ 
      searchTerm: '', 
      statuses: [...this.selectedStatuses] 
    });
  }

  // Utility methods
  getStatusConfig(status: PostStatus): StatusConfig {
    return this.calendarService.getStatusConfig(status);
  }

  formatMonthYear(date: Date): string {
    return this.calendarService.formatMonthYear(date);
  }

  getTruncatedTitle(title: string, maxLength: number = 20): string {
    return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
  }

  getPostTooltip(post: CalendarPost): string {
    let tooltip = `${post.title}\n${post.description}\nScheduled: ${post.time}\nStatus: ${post.status}`;
    if (post.projectTitle) {
      tooltip += `\nProject: ${post.projectTitle}`;
    }
    if (post.companyName) {
      tooltip += `\nCompany: ${post.companyName}`;
    }
    if (post.deadline) {
      tooltip += `\nDeadline: ${new Date(post.deadline).toLocaleDateString()}`;
    }
    return tooltip;
  }

  private refreshCalendarData(): void {
    this.calendarService.currentDate$.pipe(takeUntil(this.destroy$)).subscribe(currentDate => {
      this.calendarDays$ = this.calendarService.generateCalendarDays(currentDate);
    });
  }

  // Track by functions for performance
  trackByDate(index: number, day: CalendarDay): string {
    return day.date.toISOString();
  }

  trackByPostId(index: number, post: CalendarPost): number {
    return post.id;
  }
}
