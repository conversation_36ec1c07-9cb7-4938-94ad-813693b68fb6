#!/usr/bin/env python
import os
import django
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from core.models import User, Project, Post, Company
from datetime import datetime, date

def fix_claire_posts():
    print("=== FIXING CLAIRE'S POSTS ===")
    
    # Get Claire
    try:
        claire = User.objects.get(username='claire')
        print(f"Found Claire: {claire.username} ({claire.role})")
    except User.DoesNotExist:
        print("Claire not found!")
        return
    
    # Check assigned projects
    assigned_projects = claire.assigned_projects.all()
    print(f"Claire's assigned projects: {[p.name for p in assigned_projects]}")
    
    if not assigned_projects.exists():
        print("<PERSON> has no assigned projects! Let's assign her to one...")
        project = Project.objects.first()
        if project:
            project.creators.add(claire)
            print(f"Assigned Claire to project: {project.name}")
            assigned_projects = claire.assigned_projects.all()
        else:
            print("No projects found!")
            return
    
    # Check existing posts by <PERSON>
    claire_posts = Post.objects.filter(creator=claire)
    print(f"Total posts by Claire: {claire_posts.count()}")
    
    # Check posts in assigned projects
    posts_in_assigned = Post.objects.filter(
        creator=claire,
        project__in=assigned_projects
    )
    print(f"Posts by Claire in assigned projects: {posts_in_assigned.count()}")
    
    # If Claire has no posts, create a test post
    if posts_in_assigned.count() == 0:
        print("Creating a test post for Claire...")
        
        project = assigned_projects.first()
        
        # Create a test file
        test_file = SimpleUploadedFile(
            "test_image.jpg",
            b"fake image content",
            content_type="image/jpeg"
        )
        
        # Create test post
        post = Post.objects.create(
            title='Test Post by Claire',
            description='This is a test post created for Claire to verify the system works',
            project=project,
            creator=claire,
            scheduled_time=datetime.now(),
            scheduled_date=datetime.now(),
            media=test_file,
            status='draft'
        )
        
        print(f"✅ Created test post: {post.title} (ID: {post.id})")
        
        # Verify it shows up in the query
        posts_check = Post.objects.filter(
            creator=claire,
            project__in=assigned_projects
        )
        print(f"Posts now visible to Claire: {posts_check.count()}")
        
        for post in posts_check:
            print(f"  - {post.title} (Project: {post.project.name}, Status: {post.status})")
    
    else:
        print("Claire already has posts in assigned projects:")
        for post in posts_in_assigned:
            print(f"  - {post.title} (Project: {post.project.name}, Status: {post.status})")

if __name__ == "__main__":
    fix_claire_posts()
