<div class="sidebar" [class.collapsed]="isCollapsed">
  <!-- Header Section -->
  <div class="sidebar-header">
    <div class="logo-section" *ngIf="!isCollapsed">
      <h2 class="app-title">StreamzAI</h2>
      <span class="app-subtitle">Creator Studio</span>
    </div>
    <button 
      mat-icon-button 
      class="toggle-btn"
      (click)="toggleSidebar()"
      matTooltip="Toggle Sidebar"
      [matTooltipPosition]="isCollapsed ? 'right' : 'below'">
      <mat-icon>{{ isCollapsed ? 'menu' : 'menu_open' }}</mat-icon>
    </button>
  </div>

  <!-- Create Post CTA -->
  <div class="create-post-section">
    <button 
      mat-raised-button 
      color="primary" 
      class="create-post-btn"
      (click)="createNewPost()"
      [class.icon-only]="isCollapsed"
      matTooltip="Create New Post"
      [matTooltipPosition]="isCollapsed ? 'right' : 'below'">
      <mat-icon>add</mat-icon>
      <span *ngIf="!isCollapsed">Create Post</span>
    </button>
  </div>

  <!-- Navigation Menu -->
  <nav class="sidebar-nav">
    <ul class="nav-list">
      <li 
        *ngFor="let item of navigationItems" 
        class="nav-item"
        [class.active]="item.active">
        <button 
          mat-button 
          class="nav-button"
          (click)="navigateTo(item.route)"
          [matTooltip]="isCollapsed ? item.label : ''"
          [matTooltipPosition]="'right'">
          <mat-icon 
            class="nav-icon"
            [matBadge]="item.badge"
            [matBadgeHidden]="!item.badge"
            matBadgeColor="accent"
            matBadgeSize="small">
            {{ item.icon }}
          </mat-icon>
          <span class="nav-label" *ngIf="!isCollapsed">{{ item.label }}</span>
        </button>
      </li>
    </ul>
  </nav>

  <!-- Assigned Projects Section -->
  <div class="projects-section" *ngIf="!isCollapsed && assignedProjects.length > 0">
    <mat-divider></mat-divider>
    <div class="section-header">
      <mat-icon class="section-icon">assignment</mat-icon>
      <span class="section-title">Assigned Projects</span>
    </div>
    <div class="projects-list">
      <div
        *ngFor="let project of assignedProjects"
        class="project-item"
        matTooltip="Click to view project details"
        matTooltipPosition="right">
        <div class="project-info">
          <div class="project-name">{{ project.title || project.name }}</div>
          <div class="project-company">{{ project.company.name }}</div>
          <div class="project-admin" *ngIf="project.company_admin">
            <mat-icon class="admin-icon">person</mat-icon>
            <span class="admin-name">{{ project.company_admin.full_name }}</span>
          </div>
          <div class="project-deadline" *ngIf="project.deadline">
            <mat-icon class="deadline-icon">schedule</mat-icon>
            <span class="deadline-text">{{ formatDeadline(project.deadline) }}</span>
          </div>
        </div>
        <div class="project-stats">
          <span class="posts-count" *ngIf="project.postsCount > 0">
            {{ project.postsCount }} posts
          </span>
          <span class="pending-count" *ngIf="project.pendingPostsCount > 0">
            {{ project.pendingPostsCount }} pending
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Spacer -->
  <div class="sidebar-spacer"></div>

  <!-- User Profile Section -->
  <div class="user-profile">
    <mat-divider></mat-divider>
    <div class="profile-content">
      <button 
        mat-button 
        class="profile-button"
        [matMenuTriggerFor]="userMenu"
        [class.collapsed-profile]="isCollapsed">
        <div class="avatar-container">
          <div class="user-avatar">
            {{ getUserInitials() }}
          </div>
        </div>
        <div class="user-info" *ngIf="!isCollapsed">
          <span class="user-name">{{ getUserDisplayName() }}</span>
          <span class="user-email">{{ getUserEmail() }}</span>
        </div>
        <mat-icon class="profile-menu-icon" *ngIf="!isCollapsed">expand_more</mat-icon>
      </button>
    </div>
  </div>

  <!-- User Menu -->
  <mat-menu #userMenu="matMenu" class="user-menu">
    <button mat-menu-item>
      <mat-icon>person</mat-icon>
      <span>Profile</span>
    </button>
    <button mat-menu-item>
      <mat-icon>settings</mat-icon>
      <span>Settings</span>
    </button>
    <button mat-menu-item>
      <mat-icon>help</mat-icon>
      <span>Help</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="logout()">
      <mat-icon>logout</mat-icon>
      <span>Logout</span>
    </button>
  </mat-menu>
</div>
