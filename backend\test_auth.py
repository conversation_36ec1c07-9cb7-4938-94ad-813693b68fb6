#!/usr/bin/env python3
"""
Test script to verify authentication is working properly
"""
import requests
import json
import sys

# Configuration
BASE_URL = 'http://127.0.0.1:8000/api'
TEST_USERS = [
    {'username': 'nayana', 'expected_role': 'creator'},
    {'username': '<PERSON>', 'expected_role': 'company_admin'},
    {'username': '<PERSON>', 'expected_role': 'super_admin'},
    {'username': '<PERSON>', 'expected_role': 'creator'},
]

def test_user_authentication(username, expected_role):
    """Test authentication for a specific user"""
    print(f"\n🧪 Testing authentication for user: {username}")
    
    # Try to get token
    token_url = f"{BASE_URL}/token/"
    login_data = {
        'username': username,
        'password': username  # Using username as password (set by our fix script)
    }
    
    try:
        response = requests.post(token_url, json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Token obtained successfully")
            print(f"  📋 User role: {data.get('user', {}).get('role', 'unknown')}")
            
            # Verify role matches expected
            actual_role = data.get('user', {}).get('role')
            if actual_role == expected_role:
                print(f"  ✅ Role matches expected: {expected_role}")
            else:
                print(f"  ❌ Role mismatch! Expected: {expected_role}, Got: {actual_role}")
            
            # Test protected endpoint
            token = data.get('access')
            if token:
                headers = {'Authorization': f'Bearer {token}'}
                me_response = requests.get(f"{BASE_URL}/me/", headers=headers)
                
                if me_response.status_code == 200:
                    print(f"  ✅ Protected endpoint access successful")
                    return True
                else:
                    print(f"  ❌ Protected endpoint failed: {me_response.status_code}")
                    return False
            else:
                print(f"  ❌ No access token in response")
                return False
                
        else:
            print(f"  ❌ Authentication failed: {response.status_code}")
            print(f"  📄 Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"  ❌ Connection error - is the Django server running?")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error: {str(e)}")
        return False

def main():
    """Run authentication tests for all users"""
    print("🔐 Starting Authentication Test Suite")
    print("=" * 50)
    
    success_count = 0
    total_count = len(TEST_USERS)
    
    for user_info in TEST_USERS:
        success = test_user_authentication(
            user_info['username'], 
            user_info['expected_role']
        )
        if success:
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {success_count}/{total_count} users authenticated successfully")
    
    if success_count == total_count:
        print("🎉 All authentication tests passed!")
        sys.exit(0)
    else:
        print("⚠️  Some authentication tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
