// import { HttpInterceptorFn } from '@angular/common/http';

// export const authInterceptor: HttpInterceptorFn = (req, next) => {
//   return next(req);
// };
import { Injectable } from '@angular/core';
import {
  HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpInterceptorFn } from '@angular/common/http';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRedirecting = false;

  intercept(req: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    const accessToken = localStorage.getItem('access_token');

    // Only log for non-token requests to reduce noise
    if (!req.url.includes('/token/')) {
      console.log('💡 AuthInterceptor - Processing request to:', req.url);
    }

    if (accessToken && !req.url.includes('/token/')) {
      const clonedReq = req.clone({
        setHeaders: {
          Authorization: `Bearer ${accessToken}`
        }
      });

      return next.handle(clonedReq).pipe(
        catchError((error: HttpErrorResponse) => {
          if (error.status === 401 && !this.isRedirecting) {
            console.error('🔒 Authentication failed - clearing tokens and redirecting to login');
            this.isRedirecting = true;

            // Clear invalid tokens
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('current_user');

            // Redirect to login page
            setTimeout(() => {
              window.location.href = '/login';
            }, 100);
          }
          return throwError(() => error);
        })
      );
    }

    return next.handle(req);
  }
}
