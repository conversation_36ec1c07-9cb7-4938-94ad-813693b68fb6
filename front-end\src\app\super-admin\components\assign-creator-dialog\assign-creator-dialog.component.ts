import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SuperAdminService } from '../../services/super-admin.service';
import { Company, User, CreatorAssignmentRequest } from '../../models/super-admin.models';

@Component({
  selector: 'app-assign-creator-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <h2 mat-dialog-title>
      <mat-icon>person_add</mat-icon>
      Assign Content Creator
    </h2>

    <mat-dialog-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading data...</p>
      </div>

      <form [formGroup]="assignmentForm" class="assignment-form" *ngIf="!isLoading">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Creator</mat-label>
          <mat-select formControlName="creator_id" placeholder="Choose a creator">
            <mat-option *ngFor="let creator of availableCreators" [value]="creator.id">
              {{ creator.username }} ({{ creator.email }})
              <span *ngIf="creator.company" class="creator-company">
                - Currently: {{ creator.company.name }}
              </span>
            </mat-option>
          </mat-select>
          <mat-error *ngIf="assignmentForm.get('creator_id')?.hasError('required')">
            Please select a creator
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Company</mat-label>
          <mat-select formControlName="company_id" placeholder="Choose a company">
            <mat-option *ngFor="let company of companies" [value]="company.id">
              {{ company.name }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="assignmentForm.get('company_id')?.hasError('required')">
            Please select a company
          </mat-error>
        </mat-form-field>

        <div class="info-note" *ngIf="selectedCreator?.company">
          <mat-icon>info</mat-icon>
          <span>
            This creator is currently assigned to {{ selectedCreator?.company?.name }}.
            Assigning to a new company will update their assignment.
          </span>
        </div>
      </form>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button 
              color="primary" 
              (click)="onAssign()"
              [disabled]="assignmentForm.invalid || isLoading">
        <mat-icon>person_add</mat-icon>
        Assign Creator
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .assignment-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-width: 400px;
    }

    .full-width {
      width: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      text-align: center;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    .creator-company {
      font-size: 12px;
      color: #666;
      font-style: italic;
    }

    .info-note {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 12px;
      background-color: #e3f2fd;
      border-radius: 4px;
      font-size: 14px;
      color: #1976d2;
    }

    .info-note mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    mat-dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    mat-dialog-content {
      padding: 20px 24px;
    }

    mat-dialog-actions {
      padding: 16px 24px;
    }
  `]
})
export class AssignCreatorDialogComponent implements OnInit {
  assignmentForm: FormGroup;
  companies: Company[] = [];
  availableCreators: User[] = [];
  isLoading = true;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AssignCreatorDialogComponent>,
    private superAdminService: SuperAdminService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.assignmentForm = this.fb.group({
      creator_id: ['', Validators.required],
      company_id: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  get selectedCreator(): User | undefined {
    const creatorId = this.assignmentForm.get('creator_id')?.value;
    return this.availableCreators.find(creator => creator.id === creatorId);
  }

  loadData(): void {
    this.isLoading = true;
    
    Promise.all([
      this.superAdminService.getCompanies().toPromise(),
      this.superAdminService.getCreators().toPromise()
    ]).then(([companies, creators]) => {
      this.companies = companies || [];
      this.availableCreators = creators || [];
      this.isLoading = false;
    }).catch(error => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAssign(): void {
    if (this.assignmentForm.valid) {
      const assignment: CreatorAssignmentRequest = this.assignmentForm.value;
      this.dialogRef.close(assignment);
    }
  }
}
