import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

// Material Design Imports
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatExpansionModule } from '@angular/material/expansion';

// Services and Models
import { SuperAdminService } from './services/super-admin.service';
import {
  SuperAdminDashboardStats,
  Company,
  User,
  ActivityLog,
  QuickAction,
  Project
} from './models/super-admin.models';

// Dialog Components
import { CreateCompanyDialogComponent } from './components/create-company-dialog/create-company-dialog.component';
import { AssignAdminDialogComponent } from './components/assign-admin-dialog/assign-admin-dialog.component';
import { AssignCreatorDialogComponent } from './components/assign-creator-dialog/assign-creator-dialog.component';
import { CompanySettingsDialogComponent } from './components/company-settings-dialog/company-settings-dialog.component';
import { ProjectManagementDialogComponent } from './components/project-management-dialog/project-management-dialog.component';

@Component({
  selector: 'app-super-admin-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatToolbarModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTableModule,
    MatChipsModule,
    MatBadgeModule,
    MatGridListModule,
    MatListModule,
    MatDividerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatExpansionModule
  ],
  templateUrl: './super-admin-dashboard.component.html',
  styleUrls: ['./super-admin-dashboard.component.css']
})
export class SuperAdminDashboardComponent implements OnInit {
  // Dashboard Data
  dashboardStats: SuperAdminDashboardStats | null = null;
  companies: Company[] = [];
  recentActivities: ActivityLog[] = [];

  // Loading States
  isLoading = false;
  isLoadingStats = false;
  isLoadingActivities = false;

  // Project Management
  companyProjects: { [companyId: number]: Project[] } = {};
  loadingProjects: { [companyId: number]: boolean } = {};
  expandedCompanies: Set<number> = new Set();
  
  // Current Date
  currentDate = new Date();
  
  // Quick Actions
  quickActions: QuickAction[] = [
    {
      id: 'create-company',
      title: 'Create Company',
      description: 'Add a new company to the platform',
      icon: 'business',
      action: () => this.openCreateCompanyDialog(),
      color: 'primary'
    },
    {
      id: 'assign-admin',
      title: 'Assign Admin',
      description: 'Assign a Company Admin to a company',
      icon: 'admin_panel_settings',
      action: () => this.openAssignAdminDialog(),
      color: 'accent'
    },
    {
      id: 'assign-creator',
      title: 'Assign Creator',
      description: 'Link a Content Creator to a company',
      icon: 'person_add',
      action: () => this.openAssignCreatorDialog(),
      color: 'warn'
    }
  ];

  // Table Columns
  companiesColumns: string[] = ['name', 'admins', 'creators', 'status', 'actions'];
  activitiesColumns: string[] = ['type', 'message', 'timestamp'];

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private superAdminService: SuperAdminService
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.loadDashboardStats();
    this.loadCompanies();
    this.loadRecentActivities();
  }

  loadDashboardStats(): void {
    this.isLoadingStats = true;
    this.superAdminService.getDashboardStats().subscribe({
      next: (stats) => {
        this.dashboardStats = stats;
        this.isLoadingStats = false;
      },
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        this.showError('Failed to load dashboard statistics');
        this.isLoadingStats = false;
      }
    });
  }

  loadCompanies(): void {
    this.isLoading = true;
    this.superAdminService.getCompanies().subscribe({
      next: (companies) => {
        this.companies = companies;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading companies:', error);
        this.showError('Failed to load companies');
        this.isLoading = false;
      }
    });
  }

  loadRecentActivities(): void {
    this.isLoadingActivities = true;
    this.superAdminService.getActivityLogs().subscribe({
      next: (activities) => {
        this.recentActivities = activities;
        this.isLoadingActivities = false;
      },
      error: (error) => {
        console.error('Error loading activities:', error);
        this.showError('Failed to load recent activities');
        this.isLoadingActivities = false;
      }
    });
  }

  // Dialog Methods
  openCreateCompanyDialog(): void {
    const dialogRef = this.dialog.open(CreateCompanyDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.createCompany(result);
      }
    });
  }

  openAssignAdminDialog(): void {
    const dialogRef = this.dialog.open(AssignAdminDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.assignAdmin(result);
      }
    });
  }

  openAssignCreatorDialog(): void {
    const dialogRef = this.dialog.open(AssignCreatorDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.assignCreator(result);
      }
    });
  }

  openCompanySettingsDialog(companyId: number): void {
    const dialogRef = this.dialog.open(CompanySettingsDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: true,
      data: { companyId }
    });

    dialogRef.afterClosed().subscribe(result => {
      // Refresh dashboard data after settings dialog closes
      this.loadDashboardData();
    });
  }

  // Project Management Methods
  toggleCompanyExpansion(companyId: number): void {
    if (this.expandedCompanies.has(companyId)) {
      this.expandedCompanies.delete(companyId);
    } else {
      this.expandedCompanies.add(companyId);
      this.loadCompanyProjects(companyId);
    }
  }

  loadCompanyProjects(companyId: number): void {
    if (this.companyProjects[companyId]) {
      return; // Already loaded
    }

    this.loadingProjects[companyId] = true;
    this.superAdminService.getCompanyProjects(companyId).subscribe({
      next: (projects) => {
        this.companyProjects[companyId] = projects || [];
        this.loadingProjects[companyId] = false;
      },
      error: (error) => {
        console.error('Error loading company projects:', error);
        this.showError('Failed to load projects');
        this.companyProjects[companyId] = []; // Initialize as empty array on error
        this.loadingProjects[companyId] = false;
      }
    });
  }

  openCreateProjectDialog(companyId: number, companyName: string): void {
    // First load company settings to get available users
    this.superAdminService.getCompanySettings(companyId).subscribe({
      next: (settings) => {
        const dialogRef = this.dialog.open(ProjectManagementDialogComponent, {
          width: '700px',
          maxWidth: '90vw',
          disableClose: true,
          data: {
            companyId,
            companyName,
            availableCreators: settings.available_users.filter(u => u.role === 'creator'),
            availableAdmins: settings.available_users.filter(u => u.role === 'company_admin')
          }
        });

        dialogRef.afterClosed().subscribe(result => {
          if (result) {
            // Refresh projects for this company
            delete this.companyProjects[companyId];
            this.loadCompanyProjects(companyId);
            this.loadDashboardData();
          }
        });
      },
      error: (error) => {
        console.error('Error loading company settings:', error);
        this.showError('Failed to load company data');
      }
    });
  }

  openEditProjectDialog(project: Project, companyName: string): void {
    // Load company settings to get available users
    this.superAdminService.getCompanySettings(project.company as any).subscribe({
      next: (settings) => {
        const dialogRef = this.dialog.open(ProjectManagementDialogComponent, {
          width: '700px',
          maxWidth: '90vw',
          disableClose: true,
          data: {
            companyId: project.company as any,
            companyName,
            project,
            availableCreators: settings.available_users.filter(u => u.role === 'creator'),
            availableAdmins: settings.available_users.filter(u => u.role === 'company_admin')
          }
        });

        dialogRef.afterClosed().subscribe(result => {
          if (result) {
            // Refresh projects for this company
            delete this.companyProjects[project.company as any];
            this.loadCompanyProjects(project.company as any);
            this.loadDashboardData();
          }
        });
      },
      error: (error) => {
        console.error('Error loading company settings:', error);
        this.showError('Failed to load company data');
      }
    });
  }

  // Action Methods
  createCompany(companyData: any): void {
    this.superAdminService.createCompany(companyData).subscribe({
      next: (response) => {
        this.showSuccess('Company created successfully!');
        this.loadDashboardData(); // Refresh all data
      },
      error: (error) => {
        console.error('Error creating company:', error);
        this.showError('Failed to create company');
      }
    });
  }

  assignAdmin(assignment: any): void {
    this.superAdminService.assignAdmin(assignment).subscribe({
      next: (response) => {
        this.showSuccess('Admin assigned successfully!');
        this.loadDashboardData(); // Refresh all data
      },
      error: (error) => {
        console.error('Error assigning admin:', error);
        this.showError('Failed to assign admin');
      }
    });
  }

  assignCreator(assignment: any): void {
    this.superAdminService.assignCreatorToCompany(assignment).subscribe({
      next: (response) => {
        this.showSuccess('Creator assigned successfully!');
        this.loadDashboardData(); // Refresh all data
      },
      error: (error) => {
        console.error('Error assigning creator:', error);
        this.showError('Failed to assign creator');
      }
    });
  }

  // Utility Methods
  showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  formatDate(dateString: string): string {
    return this.superAdminService.formatDate(dateString);
  }

  getActivityIcon(type: string): string {
    return this.superAdminService.getActivityIcon(type);
  }

  getActivityColor(type: string): string {
    return this.superAdminService.getActivityColor(type);
  }

  logout(): void {
    localStorage.clear();
    this.router.navigate(['/login']);
  }

  // Getters for template
  getTotalCompanies(): number {
    return this.dashboardStats?.companies?.total || 0;
  }

  getActiveCompanies(): number {
    return this.dashboardStats?.companies?.active || 0;
  }

  getTotalAdmins(): number {
    return this.dashboardStats?.admins?.total || 0;
  }

  getActiveAdmins(): number {
    return this.dashboardStats?.admins?.active || 0;
  }

  getTotalCreators(): number {
    return this.dashboardStats?.creators?.total || 0;
  }

  getAssignedCreators(): number {
    return this.dashboardStats?.creators?.assigned || 0;
  }

  getTotalPosts(): number {
    return this.dashboardStats?.content?.total_posts || 0;
  }

  getPendingReviews(): number {
    return this.dashboardStats?.content?.pending_reviews || 0;
  }

  // Helper method to safely get company projects
  getCompanyProjects(companyId: number): Project[] {
    return this.companyProjects[companyId] || [];
  }
}
