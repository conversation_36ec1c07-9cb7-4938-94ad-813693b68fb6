<!-- Dashboard Header -->
<mat-toolbar class="dashboard-header" color="primary">
  <div class="header-title">
    <mat-icon>admin_panel_settings</mat-icon>
    <span>Super Admin Dashboard</span>
  </div>
  <span class="spacer"></span>
  <button mat-icon-button (click)="logout()" matTooltip="Logout">
    <mat-icon>logout</mat-icon>
  </button>
</mat-toolbar>

<!-- Loading Spinner -->
<div *ngIf="isLoadingStats" class="loading-container">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading dashboard...</p>
</div>

<!-- Main Dashboard Content -->
<div *ngIf="!isLoadingStats" class="dashboard-container">

  <!-- Welcome Section -->
  <mat-card class="welcome-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>waving_hand</mat-icon>
        Welcome back, Super Admin!
      </mat-card-title>
      <mat-card-subtitle>
        Platform Management Dashboard • {{ currentDate | date:'fullDate' }}
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>Manage companies, assign admins, and oversee the entire content approval platform.</p>
    </mat-card-content>
  </mat-card>

  <!-- Statistics Cards -->
  <div class="stats-grid">
    <mat-card class="stat-card companies-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>business</mat-icon>
          Companies
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalCompanies() }}</span>
          <span class="stat-label">Total Companies</span>
        </div>
        <div class="stat-item">
          <span class="stat-number active">{{ getActiveCompanies() }}</span>
          <span class="stat-label">Active Companies</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card admins-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>admin_panel_settings</mat-icon>
          Company Admins
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalAdmins() }}</span>
          <span class="stat-label">Total Admins</span>
        </div>
        <div class="stat-item">
          <span class="stat-number active">{{ getActiveAdmins() }}</span>
          <span class="stat-label">Active Admins</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card creators-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>people</mat-icon>
          Content Creators
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalCreators() }}</span>
          <span class="stat-label">Total Creators</span>
        </div>
        <div class="stat-item">
          <span class="stat-number assigned">{{ getAssignedCreators() }}</span>
          <span class="stat-label">Assigned</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>article</mat-icon>
          Content Overview
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalPosts() }}</span>
          <span class="stat-label">Total Posts</span>
        </div>
        <div class="stat-item">
          <span class="stat-number pending" [matBadge]="getPendingReviews()"
                matBadgeColor="warn" [matBadgeHidden]="getPendingReviews() === 0">
            {{ getPendingReviews() }}
          </span>
          <span class="stat-label">Pending Reviews</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Quick Actions Panel -->
  <mat-card class="quick-actions-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>flash_on</mat-icon>
        Quick Actions
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="action-grid">
        <button *ngFor="let action of quickActions" 
                mat-raised-button 
                [color]="action.color"
                (click)="action.action()"
                class="action-button">
          <mat-icon>{{ action.icon }}</mat-icon>
          <div class="action-content">
            <span class="action-title">{{ action.title }}</span>
            <span class="action-description">{{ action.description }}</span>
          </div>
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Main Content Section -->
  <div class="content-section">
    <!-- Company Management Panel -->
    <mat-card class="companies-panel">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>business</mat-icon>
          Company Management
        </mat-card-title>
        <div class="spacer"></div>
        <button mat-icon-button (click)="openCreateCompanyDialog()" matTooltip="Create Company">
          <mat-icon>add</mat-icon>
        </button>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="isLoading" class="loading-section">
          <mat-spinner diameter="30"></mat-spinner>
          <p>Loading companies...</p>
        </div>

        <div *ngIf="!isLoading && companies.length === 0" class="empty-state">
          <mat-icon>business</mat-icon>
          <h3>No Companies Yet</h3>
          <p>Create your first company to get started.</p>
          <button mat-raised-button color="primary" (click)="openCreateCompanyDialog()">
            <mat-icon>add</mat-icon>
            Create Company
          </button>
        </div>

        <!-- Expandable Company List with Projects -->
        <mat-accordion *ngIf="!isLoading && companies.length > 0" class="company-accordion">
          <mat-expansion-panel *ngFor="let company of companies"
                               class="company-panel"
                               [expanded]="expandedCompanies.has(company.id)"
                               (opened)="toggleCompanyExpansion(company.id)"
                               (closed)="expandedCompanies.delete(company.id)">

            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon>business</mat-icon>
                <span class="company-name">{{ company.name }}</span>
              </mat-panel-title>
              <mat-panel-description>
                <mat-chip-set>
                  <mat-chip>{{ company.admin_count || 0 }} Admins</mat-chip>
                  <mat-chip>{{ company.creator_count || 0 }} Creators</mat-chip>
                  <mat-chip>{{ getCompanyProjects(company.id).length }} Projects</mat-chip>
                </mat-chip-set>
                <button mat-icon-button
                        matTooltip="Manage Company Settings"
                        (click)="$event.stopPropagation(); openCompanySettingsDialog(company.id)"
                        class="settings-button">
                  <mat-icon>settings</mat-icon>
                </button>
              </mat-panel-description>
            </mat-expansion-panel-header>

            <!-- Projects Content -->
            <div class="projects-content">
              <div *ngIf="loadingProjects[company.id]" class="loading-projects">
                <mat-spinner diameter="30"></mat-spinner>
                <p>Loading projects...</p>
              </div>

              <div *ngIf="!loadingProjects[company.id]" class="projects-list">
                <!-- Existing Projects -->
                <div *ngIf="getCompanyProjects(company.id).length > 0; else noProjects" class="project-items">
                  <div *ngFor="let project of getCompanyProjects(company.id)"
                       class="project-item"
                       (click)="openEditProjectDialog(project, company.name)">
                    <div class="project-header">
                      <mat-icon>folder</mat-icon>
                      <div class="project-info">
                        <h4>{{ project.title || project.name }}</h4>
                        <p class="project-description">{{ project.description || 'No description' }}</p>
                      </div>
                    </div>
                    <div class="project-meta">
                      <span class="deadline" *ngIf="project.deadline">
                        <mat-icon>schedule</mat-icon>
                        {{ project.deadline | date:'shortDate' }}
                      </span>
                      <span class="creators-count">
                        <mat-icon>people</mat-icon>
                        {{ project.creators.length || 0 }} creators
                      </span>
                    </div>
                  </div>
                </div>

                <ng-template #noProjects>
                  <div class="no-projects">
                    <mat-icon>folder_open</mat-icon>
                    <p>No projects found for this company</p>
                  </div>
                </ng-template>

                <!-- Add Project Button -->
                <mat-divider></mat-divider>
                <button mat-raised-button
                        color="primary"
                        class="add-project-button"
                        (click)="openCreateProjectDialog(company.id, company.name)">
                  <mat-icon>add</mat-icon>
                  Add Project
                </button>
              </div>
            </div>
          </mat-expansion-panel>
        </mat-accordion>
      </mat-card-content>
    </mat-card>

    <!-- Activity & Logs -->
    <mat-card class="activity-panel">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Recent Activity
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="isLoadingActivities" class="loading-section">
          <mat-spinner diameter="30"></mat-spinner>
          <p>Loading activities...</p>
        </div>

        <div *ngIf="!isLoadingActivities && recentActivities.length === 0" class="empty-state">
          <mat-icon>history</mat-icon>
          <h3>No Recent Activity</h3>
          <p>Activity will appear here as you manage the platform.</p>
        </div>

        <mat-list *ngIf="!isLoadingActivities && recentActivities.length > 0">
          <mat-list-item *ngFor="let activity of recentActivities" class="activity-item">
            <mat-icon matListItemIcon [color]="getActivityColor(activity.type)">
              {{ getActivityIcon(activity.type) }}
            </mat-icon>
            <div matListItemTitle>{{ activity.message }}</div>
            <div matListItemLine>{{ formatDate(activity.timestamp) }}</div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>
  </div>
</div>
