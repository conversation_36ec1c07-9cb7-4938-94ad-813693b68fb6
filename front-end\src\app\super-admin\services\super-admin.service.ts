import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  SuperAdminDashboardStats,
  Company,
  User,
  ActivityLog,
  AssignmentRequest,
  CreatorAssignmentRequest,
  CompanyCreationRequest,
  ApiResponse,
  ProjectCreationRequest,
  CompanySettings,
  Project
} from '../models/super-admin.models';

@Injectable({
  providedIn: 'root'
})
export class SuperAdminService {
  private readonly API_BASE = 'http://127.0.0.1:8000/api';
  private readonly SUPER_ADMIN_API = `${this.API_BASE}/super-admin`;

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Dashboard Statistics
  getDashboardStats(): Observable<SuperAdminDashboardStats> {
    return this.http.get<SuperAdminDashboardStats>(
      `${this.SUPER_ADMIN_API}/dashboard_stats/`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Company Management
  getCompanies(): Observable<Company[]> {
    return this.http.get<Company[]>(
      `${this.SUPER_ADMIN_API}/list_companies/`,
      { headers: this.getAuthHeaders() }
    );
  }

  createCompany(companyData: CompanyCreationRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(
      `${this.SUPER_ADMIN_API}/add_company/`,
      companyData,
      { headers: this.getAuthHeaders() }
    );
  }

  // User Management
  getUsers(role?: string): Observable<User[]> {
    const url = role 
      ? `${this.SUPER_ADMIN_API}/list_users/?role=${role}`
      : `${this.SUPER_ADMIN_API}/list_users/`;
    
    return this.http.get<User[]>(url, { headers: this.getAuthHeaders() });
  }

  getCompanyAdmins(): Observable<User[]> {
    return this.getUsers('company_admin');
  }

  getCreators(): Observable<User[]> {
    return this.getUsers('creator');
  }

  // Assignment Operations
  assignAdmin(assignment: AssignmentRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(
      `${this.SUPER_ADMIN_API}/assign_admin/`,
      assignment,
      { headers: this.getAuthHeaders() }
    );
  }

  assignCreatorToCompany(assignment: CreatorAssignmentRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(
      `${this.SUPER_ADMIN_API}/assign_creator_to_company/`,
      assignment,
      { headers: this.getAuthHeaders() }
    );
  }

  // Activity Logs
  getActivityLogs(): Observable<ActivityLog[]> {
    return this.http.get<ActivityLog[]>(
      `${this.SUPER_ADMIN_API}/activity_logs/`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Project Management
  createProject(projectData: ProjectCreationRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(
      `${this.SUPER_ADMIN_API}/create_project/`,
      projectData,
      { headers: this.getAuthHeaders() }
    );
  }

  updateProject(projectId: number, projectData: ProjectCreationRequest): Observable<ApiResponse> {
    return this.http.put<ApiResponse>(
      `${this.SUPER_ADMIN_API}/update_project/${projectId}/`,
      projectData,
      { headers: this.getAuthHeaders() }
    );
  }

  getCompanyProjects(companyId: number): Observable<Project[]> {
    return this.http.get<Project[]>(
      `${this.SUPER_ADMIN_API}/list_projects/${companyId}/`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Company Settings
  getCompanySettings(companyId: number): Observable<CompanySettings> {
    return this.http.get<CompanySettings>(
      `${this.SUPER_ADMIN_API}/${companyId}/company_settings/`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Get users eligible for admin assignment
  getUsersEligibleForAdmin(): Observable<User[]> {
    return this.http.get<User[]>(
      `${this.SUPER_ADMIN_API}/list_users/?eligible_for_admin=true`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Utility Methods
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getActivityIcon(type: string): string {
    switch (type) {
      case 'admin_assignment':
        return 'admin_panel_settings';
      case 'creator_assignment':
        return 'person_add';
      case 'company_creation':
        return 'business';
      case 'user_creation':
        return 'person';
      default:
        return 'info';
    }
  }

  getActivityColor(type: string): string {
    switch (type) {
      case 'admin_assignment':
        return 'primary';
      case 'creator_assignment':
        return 'accent';
      case 'company_creation':
        return 'warn';
      default:
        return 'primary';
    }
  }
}
