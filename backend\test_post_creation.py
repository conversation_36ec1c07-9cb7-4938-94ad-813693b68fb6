#!/usr/bin/env python
import os
import django
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from core.models import User, Project, Post, Company
from core.serializers import PostSerializer
from datetime import datetime, date

def test_post_creation():
    print("=== TEST POST CREATION ===")
    
    # Get a creator user
    creator = User.objects.filter(role='creator').first()
    if not creator:
        print("No creator found!")
        return
    
    print(f"Testing with creator: {creator.username}")
    
    # Get assigned projects
    assigned_projects = creator.assigned_projects.all()
    print(f"Assigned projects: {[p.name for p in assigned_projects]}")
    
    if not assigned_projects.exists():
        print("Creator has no assigned projects!")
        # Let's assign the creator to a project
        project = Project.objects.first()
        if project:
            project.creators.add(creator)
            print(f"Assigned creator to project: {project.name}")
        else:
            print("No projects found!")
            return
    
    project = assigned_projects.first()
    print(f"Using project: {project.name}")
    
    # Create a test file
    test_file = SimpleUploadedFile(
        "test_image.jpg",
        b"fake image content",
        content_type="image/jpeg"
    )
    
    # Test data
    post_data = {
        'title': 'Test Post',
        'description': 'This is a test post created via script',
        'project': project.id,
        'creator': creator.id,
        'scheduled_time': datetime.now(),
        'scheduled_date': date.today(),
        'media': test_file
    }
    
    print(f"Creating post with data: {post_data}")
    
    # Create post using serializer
    serializer = PostSerializer(data=post_data)
    if serializer.is_valid():
        post = serializer.save()
        print(f"✅ Post created successfully: {post.title} (ID: {post.id})")
        
        # Verify it shows up in my_posts query
        my_posts = Post.objects.filter(
            creator=creator,
            project__in=assigned_projects
        )
        print(f"Posts visible to creator: {my_posts.count()}")
        
    else:
        print(f"❌ Serializer errors: {serializer.errors}")

if __name__ == "__main__":
    test_post_creation()
