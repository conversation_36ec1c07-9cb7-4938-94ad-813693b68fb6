<div class="p-6 max-w-xl mx-auto bg-white rounded-xl shadow space-y-4">
  <h2 class="text-xl font-bold">Create New Post</h2>

  <form [formGroup]="postForm" (ngSubmit)="onSubmit()" enctype="multipart/form-data">

    <!-- Title Field -->
    <div class="mb-4">
      <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
      <input
        type="text"
        id="title"
        formControlName="title"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholder="Enter post title"
      />
      <div *ngIf="postForm.get('title')?.invalid && postForm.get('title')?.touched" class="text-red-500 text-sm mt-1">
        Title is required
      </div>
    </div>

    <!-- Description Field -->
    <div class="mb-4">
      <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
      <textarea
        id="description"
        formControlName="description"
        rows="4"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholder="Enter post description"
      ></textarea>
      <div *ngIf="postForm.get('description')?.invalid && postForm.get('description')?.touched" class="text-red-500 text-sm mt-1">
        Description is required
      </div>
    </div>

    <!-- Project Selection -->
    <div class="mb-4">
      <label for="project" class="block text-sm font-medium text-gray-700 mb-2">Project *</label>
      <select
        id="project"
        formControlName="project"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="">Select a project</option>
        <option *ngFor="let project of assignedProjects" [value]="project.id">
          {{ project.name }}
        </option>
      </select>
      <div *ngIf="postForm.get('project')?.invalid && postForm.get('project')?.touched" class="text-red-500 text-sm mt-1">
        Project selection is required
      </div>
    </div>

    <!-- Scheduled Time Field -->
    <div class="mb-4">
      <label for="scheduled_time" class="block text-sm font-medium text-gray-700 mb-2">Scheduled Time *</label>
      <input
        type="datetime-local"
        id="scheduled_time"
        formControlName="scheduled_time"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <div *ngIf="postForm.get('scheduled_time')?.invalid && postForm.get('scheduled_time')?.touched" class="text-red-500 text-sm mt-1">
        Scheduled time is required
      </div>
    </div>

    <!-- File Upload Fields -->
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">Upload Images</label>
      <input type="file" (change)="onImageSelected($event)" multiple accept="image/*"
             class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
    </div>

    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">Upload Videos</label>
      <input type="file" (change)="onVideoSelected($event)" multiple accept="video/*"
             class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
    </div>

  <button
    type="submit"
    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
  >
    Create Post
  </button>
</form>

</div>
