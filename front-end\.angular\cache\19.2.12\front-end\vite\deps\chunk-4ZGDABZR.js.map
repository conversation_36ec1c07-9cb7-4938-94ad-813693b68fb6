{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/keycodes.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/coercion/private.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/observers.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/layout.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/a11y.mjs", "../../../../../../node_modules/@angular/material/fesm2022/core.mjs"], "sourcesContent": ["const MAC_ENTER = 3;\nconst BACKSPACE = 8;\nconst TAB = 9;\nconst NUM_CENTER = 12;\nconst ENTER = 13;\nconst SHIFT = 16;\nconst CONTROL = 17;\nconst ALT = 18;\nconst PAUSE = 19;\nconst CAPS_LOCK = 20;\nconst ESCAPE = 27;\nconst SPACE = 32;\nconst PAGE_UP = 33;\nconst PAGE_DOWN = 34;\nconst END = 35;\nconst HOME = 36;\nconst LEFT_ARROW = 37;\nconst UP_ARROW = 38;\nconst RIGHT_ARROW = 39;\nconst DOWN_ARROW = 40;\nconst PLUS_SIGN = 43;\nconst PRINT_SCREEN = 44;\nconst INSERT = 45;\nconst DELETE = 46;\nconst ZERO = 48;\nconst ONE = 49;\nconst TWO = 50;\nconst THREE = 51;\nconst FOUR = 52;\nconst FIVE = 53;\nconst SIX = 54;\nconst SEVEN = 55;\nconst EIGHT = 56;\nconst NINE = 57;\nconst FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nconst FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nconst QUESTION_MARK = 63;\nconst AT_SIGN = 64;\nconst A = 65;\nconst B = 66;\nconst C = 67;\nconst D = 68;\nconst E = 69;\nconst F = 70;\nconst G = 71;\nconst H = 72;\nconst I = 73;\nconst J = 74;\nconst K = 75;\nconst L = 76;\nconst M = 77;\nconst N = 78;\nconst O = 79;\nconst P = 80;\nconst Q = 81;\nconst R = 82;\nconst S = 83;\nconst T = 84;\nconst U = 85;\nconst V = 86;\nconst W = 87;\nconst X = 88;\nconst Y = 89;\nconst Z = 90;\nconst META = 91; // WIN_KEY_LEFT\nconst MAC_WK_CMD_LEFT = 91;\nconst MAC_WK_CMD_RIGHT = 93;\nconst CONTEXT_MENU = 93;\nconst NUMPAD_ZERO = 96;\nconst NUMPAD_ONE = 97;\nconst NUMPAD_TWO = 98;\nconst NUMPAD_THREE = 99;\nconst NUMPAD_FOUR = 100;\nconst NUMPAD_FIVE = 101;\nconst NUMPAD_SIX = 102;\nconst NUMPAD_SEVEN = 103;\nconst NUMPAD_EIGHT = 104;\nconst NUMPAD_NINE = 105;\nconst NUMPAD_MULTIPLY = 106;\nconst NUMPAD_PLUS = 107;\nconst NUMPAD_MINUS = 109;\nconst NUMPAD_PERIOD = 110;\nconst NUMPAD_DIVIDE = 111;\nconst F1 = 112;\nconst F2 = 113;\nconst F3 = 114;\nconst F4 = 115;\nconst F5 = 116;\nconst F6 = 117;\nconst F7 = 118;\nconst F8 = 119;\nconst F9 = 120;\nconst F10 = 121;\nconst F11 = 122;\nconst F12 = 123;\nconst NUM_LOCK = 144;\nconst SCROLL_LOCK = 145;\nconst FIRST_MEDIA = 166;\nconst FF_MINUS = 173;\nconst MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nconst VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nconst VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nconst FF_MUTE = 181;\nconst FF_VOLUME_DOWN = 182;\nconst LAST_MEDIA = 183;\nconst FF_VOLUME_UP = 183;\nconst SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nconst EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nconst COMMA = 188;\nconst DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nconst PERIOD = 190;\nconst SLASH = 191;\nconst APOSTROPHE = 192;\nconst TILDE = 192;\nconst OPEN_SQUARE_BRACKET = 219;\nconst BACKSLASH = 220;\nconst CLOSE_SQUARE_BRACKET = 221;\nconst SINGLE_QUOTE = 222;\nconst MAC_META = 224;\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n  if (modifiers.length) {\n    return modifiers.some(modifier => event[modifier]);\n  }\n  return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A, ALT, APOSTROPHE, AT_SIGN, B, BACKSLASH, BACKSPACE, C, CAPS_LOCK, CLOSE_SQUARE_BRACKET, COMMA, CONTEXT_MENU, CONTROL, D, DASH, DELETE, DOWN_ARROW, E, EIGHT, END, ENTER, EQUALS, ESCAPE, F, F1, F10, F11, F12, F2, F3, F4, F5, F6, F7, F8, F9, FF_EQUALS, FF_MINUS, FF_MUTE, FF_SEMICOLON, FF_VOLUME_DOWN, FF_VOLUME_UP, FIRST_MEDIA, FIVE, FOUR, G, H, HOME, I, INSERT, J, K, L, LAST_MEDIA, LEFT_ARROW, M, MAC_ENTER, MAC_META, MAC_WK_CMD_LEFT, MAC_WK_CMD_RIGHT, META, MUTE, N, NINE, NUMPAD_DIVIDE, NUMPAD_EIGHT, NUMPAD_FIVE, NUMPAD_FOUR, NUMPAD_MINUS, NUMPAD_MULTIPLY, NUMPAD_NINE, NUMPAD_ONE, NUMPAD_PERIOD, NUMPAD_PLUS, NUMPAD_SEVEN, NUMPAD_SIX, NUMPAD_THREE, NUMPAD_TWO, NUMPAD_ZERO, NUM_CENTER, NUM_LOCK, O, ONE, OPEN_SQUARE_BRACKET, P, PAGE_DOWN, PAGE_UP, PAUSE, PERIOD, PLUS_SIGN, PRINT_SCREEN, Q, QUESTION_MARK, R, RIGHT_ARROW, S, SCROLL_LOCK, SEMICOLON, SEVEN, SHIFT, SINGLE_QUOTE, SIX, SLASH, SPACE, T, TAB, THREE, TILDE, TWO, U, UP_ARROW, V, VOLUME_DOWN, VOLUME_UP, W, X, Y, Z, ZERO, hasModifierKey };\n", "import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n  if (!isObservable(data)) {\n    return of(data);\n  }\n  return data;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { coerceObservable };\n", "import { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\n\n// Angular may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MutationObserverFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MutationObserverFactory,\n    factory: MutationObserverFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  _mutationObserverFactory = inject(MutationObserverFactory);\n  /** Keeps track of the existing MutationObservers so they can be reused. */\n  _observedElements = new Map();\n  _ngZone = inject(NgZone);\n  constructor() {}\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContentObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContentObserver,\n    factory: ContentObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  _contentObserver = inject(ContentObserver);\n  _elementRef = inject(ElementRef);\n  /** Event emitted for each change in the element's content. */\n  event = new EventEmitter();\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  _disabled = false;\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  _debounce;\n  _currentSubscription = null;\n  constructor() {}\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkObserveContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkObserveContent,\n    selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n    inputs: {\n      disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n      debounce: \"debounce\"\n    },\n    outputs: {\n      event: \"cdkObserveContent\"\n    },\n    exportAs: [\"cdkObserveContent\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], () => [], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ObserversModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ObserversModule,\n    imports: [CdkObserveContent],\n    exports: [CdkObserveContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MutationObserverFactory]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n", "import * as i0 from '@angular/core';\nimport { NgModule, inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { Platform } from '@angular/cdk/platform';\nclass LayoutModule {\n  static ɵfac = function LayoutModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: LayoutModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n  _platform = inject(Platform);\n  _nonce = inject(CSP_NONCE, {\n    optional: true\n  });\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  _matchMedia;\n  constructor() {\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n    // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query, this._nonce);\n    }\n    return this._matchMedia(query);\n  }\n  static ɵfac = function MediaMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MediaMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaMatcher,\n    factory: MediaMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n  _mediaMatcher = inject(MediaMatcher);\n  _zone = inject(NgZone);\n  /**  A map of all media queries currently being listened for. */\n  _queries = new Map();\n  /** A subject for all other observables to takeUntil based on. */\n  _destroySubject = new Subject();\n  constructor() {}\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n    const mql = this._mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject));\n    // Add the MediaQueryList to the set of queries.\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n    this._queries.set(query, output);\n    return output;\n  }\n  static ɵfac = function BreakpointObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BreakpointObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BreakpointObserver,\n    factory: BreakpointObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };\n", "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, signal, QueryList, isSignal, effect, InjectionToken, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, RendererFactory2, EventEmitter, Output, NgModule } from '@angular/core';\nimport { Platform, _getFocusedElementPierceShadowDom, _getEventTarget, _bindEventWithOptions, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { A, Z, ZERO, NINE, hasModifierKey, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, isObservable, of, BehaviorSubject } from 'rxjs';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceObservable } from '@angular/cdk/coercion/private';\nimport { ContentObserver, ObserversModule } from '@angular/cdk/observers';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  /** Map of all registered message elements that have been placed into the document. */\n  _messageRegistry = new Map();\n  /** Container for all registered messages. */\n  _messagesContainer = null;\n  /** Unique ID for the service. */\n  _id = `${nextId++}`;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AriaDescriber)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AriaDescriber,\n    factory: AriaDescriber.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  _letterKeyStream = new Subject();\n  _items = [];\n  _selectedItemIndex = -1;\n  /** Buffer for the letters that the user has pressed */\n  _pressedLetters = [];\n  _skipPredicateFn;\n  _selectedItem = new Subject();\n  selectedItem = this._selectedItem;\n  constructor(initialItems, config) {\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  _items;\n  _activeItemIndex = -1;\n  _activeItem = signal(null);\n  _wrap = false;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _itemChangesSubscription;\n  _vertical = true;\n  _horizontal;\n  _allowedModifierKeys = [];\n  _homeAndEnd = false;\n  _pageUpAndDown = {\n    enabled: false,\n    delta: 10\n  };\n  _effectRef;\n  _typeahead;\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  _skipPredicateFn = item => item.disabled;\n  constructor(_items, injector) {\n    this._items = _items;\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut = new Subject();\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject();\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._typeahead?.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem();\n    this.updateActiveItem(item);\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem();\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nclass FocusKeyManager extends ListKeyManager {\n  _origin = 'program';\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  /** The index of the currently active (focused) item. */\n  _activeItemIndex = -1;\n  /** The currently active (focused) item. */\n  _activeItem = null;\n  /** Whether or not we activate the item when it's focused. */\n  _shouldActivationFollowFocus = false;\n  /**\n   * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n   * Right arrow are switched.\n   */\n  _horizontalOrientation = 'ltr';\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager.\n   *\n   * The default value for this doesn't skip any elements in order to keep tree items focusable\n   * when disabled. This aligns with ARIA guidelines:\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n   */\n  _skipPredicateFn = _item => false;\n  /** Function to determine equivalent items. */\n  _trackByFn = item => item;\n  /** Synchronous cache of the items to manage. */\n  _items = [];\n  _typeahead;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _hasInitialFocused = false;\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Stream that emits any time the focused item changes. */\n  change = new Subject();\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n/** @docs-private */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/** @docs-private */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  _isNoopTreeKeyManager = true;\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  change = new Subject();\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  _platform = inject(Platform);\n  constructor() {}\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InteractivityChecker)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  _element;\n  _checker;\n  _ngZone;\n  _document;\n  _injector;\n  _startAnchor;\n  _endAnchor;\n  _hasAttached = false;\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  startAnchorListener = () => this.focusLastTabbableElement();\n  endAnchorListener = () => this.focusFirstTabbableElement();\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  _enabled = true;\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _injector = inject(Injector);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  /** Underlying FocusTrap instance. */\n  focusTrap;\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  _previouslyFocusedElement = null;\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  autoCapture;\n  constructor() {\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTrapFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTrapFocus,\n    selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n    inputs: {\n      enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n      autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n    },\n    exportAs: [\"cdkTrapFocus\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], () => [], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  _focusTrapManager;\n  _inertStrategy;\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  /** Focus event handler. */\n  _listener = null;\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  _focusTrapStack = [];\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapManager)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _focusTrapManager = inject(FocusTrapManager);\n  _document = inject(DOCUMENT);\n  _inertStrategy;\n  _injector = inject(Injector);\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n      optional: true\n    });\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ConfigurableFocusTrapFactory,\n    factory: ConfigurableFocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  _platform = inject(Platform);\n  _listenerCleanups;\n  /** Emits whenever an input modality is detected. */\n  modalityDetected;\n  /** Emits when the input modality changes. */\n  modalityChanged;\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget = null;\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  _modality = new BehaviorSubject(null);\n  /** Options for this InputModalityDetector. */\n  _options;\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  _lastTouchMs = 0;\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  _onKeydown = event => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onMousedown = event => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onTouchstart = event => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n      optional: true\n    });\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n  static ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputModalityDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputModalityDetector,\n    factory: InputModalityDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  _ngZone = inject(NgZone);\n  _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _liveElement;\n  _document = inject(DOCUMENT);\n  _previousTimeout;\n  _currentPromise;\n  _currentResolve;\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n      optional: true\n    });\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LiveAnnouncer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LiveAnnouncer,\n    factory: LiveAnnouncer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  _elementRef = inject(ElementRef);\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _contentObserver = inject(ContentObserver);\n  _ngZone = inject(NgZone);\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  _politeness = 'polite';\n  /** Time in milliseconds after which to clear out the announcer element. */\n  duration;\n  _previousAnnouncedText;\n  _subscription;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAriaLive)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAriaLive,\n    selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n    inputs: {\n      politeness: [0, \"cdkAriaLive\", \"politeness\"],\n      duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n    },\n    exportAs: [\"cdkAriaLive\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], () => [], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _inputModalityDetector = inject(InputModalityDetector);\n  /** The focus origin that the next focus event is a result of. */\n  _origin = null;\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  _lastFocusOrigin;\n  /** Whether the window has just been focused. */\n  _windowFocused = false;\n  /** The timeout id of the window focus timeout. */\n  _windowFocusTimeoutId;\n  /** The timeout id of the origin clearing timeout. */\n  _originTimeoutId;\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  _originFromTouchInteraction = false;\n  /** Map of elements being monitored to their info. */\n  _elementInfo = new Map();\n  /** The number of elements currently being monitored. */\n  _monitoredElementCount = 0;\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  _rootNodeFocusListenerCount = new Map();\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  _detectionMode;\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n  };\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** Subject for stopping our InputModalityDetector subscription. */\n  _stopInputModalityDetector = new Subject();\n  constructor() {\n    const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _rootNodeFocusAndBlurListener = event => {\n    const target = _getEventTarget(event);\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event, element);\n      } else {\n        this._onBlur(event, element);\n      }\n    }\n  };\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusMonitor,\n    factory: FocusMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  _elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _monitorSubscription;\n  _focusOrigin = null;\n  cdkFocusChange = new EventEmitter();\n  constructor() {}\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkMonitorFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkMonitorFocus,\n    selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n    outputs: {\n      cdkFocusChange: \"cdkFocusChange\"\n    },\n    exportAs: [\"cdkMonitorFocus\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], () => [], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  _platform = inject(Platform);\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  _hasCheckedHighContrastMode;\n  _document = inject(DOCUMENT);\n  _breakpointSubscription;\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HighContrastModeDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HighContrastModeDetector,\n    factory: HighContrastModeDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || A11yModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: A11yModule,\n    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ObserversModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  _appId = inject(APP_ID);\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n  static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _IdGenerator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _IdGenerator,\n    factory: _IdGenerator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusMonitorDetectionMode, FocusTrap, FocusTrapFactory, HighContrastMode, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, TREE_KEY_MANAGER_FACTORY, TREE_KEY_MANAGER_FACTORY_PROVIDER, TreeKeyManager, _IdGenerator, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };\n", "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, LOCALE_ID, Injectable, Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, ElementRef, ANIMATION_MODULE_TYPE, NgZone, Injector, Input, booleanAttribute, ChangeDetectorRef, EventEmitter, isSignal, Output, ViewChild, RendererFactory2 } from '@angular/core';\nimport { HighContrastModeDetector, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, _IdGenerator } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { normalizePassiveListenerOptions, _getEventTarget, Platform, _bindEventWithOptions } from '@angular/cdk/platform';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { DOCUMENT } from '@angular/common';\n\n/** Current version of Angular Material. */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nconst _c5 = [\"mat-internal-form-field\", \"\"];\nconst _c6 = [\"*\"];\nconst VERSION = new Version('19.2.0');\n\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nclass AnimationCurves {\n  static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n}\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nclass AnimationDurations {\n  static COMPLEX = '375ms';\n  static ENTERING = '225ms';\n  static EXITING = '195ms';\n}\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nclass MatCommonModule {\n  constructor() {\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function MatCommonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCommonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCommonModule,\n    imports: [BidiModule],\n    exports: [BidiModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BidiModule, BidiModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n  _defaultMatcher;\n  ngControl;\n  _parentFormGroup;\n  _parentForm;\n  _stateChanges;\n  /** Whether the tracker is currently in an error state. */\n  errorState = false;\n  /** User-defined matcher for the error state. */\n  matcher;\n  constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n    this._defaultMatcher = _defaultMatcher;\n    this.ngControl = ngControl;\n    this._parentFormGroup = _parentFormGroup;\n    this._parentForm = _parentForm;\n    this._stateChanges = _stateChanges;\n  }\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? this.ngControl.control : null;\n    const newState = matcher?.isErrorState(control, parent) ?? false;\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\nconst NOT_IMPLEMENTED = 'Method not implemented';\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  /** The locale to use for all dates. */\n  locale;\n  _localeChanges = new Subject();\n  /** A stream that emits when the locale changes. */\n  localeChanges = this._localeChanges;\n  /**\n   * Sets the time of one date to the time of another.\n   * @param target Date whose time will be set.\n   * @param hours New hours to set on the date object.\n   * @param minutes New minutes to set on the date object.\n   * @param seconds New seconds to set on the date object.\n   */\n  setTime(target, hours, minutes, seconds) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the hours component of the given date.\n   * @param date The date to extract the hours from.\n   */\n  getHours(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the minutes component of the given date.\n   * @param date The date to extract the minutes from.\n   */\n  getMinutes(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the seconds component of the given date.\n   * @param date The date to extract the seconds from.\n   */\n  getSeconds(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Parses a date with a specific time from a user-provided value.\n   * @param value The value to parse.\n   * @param parseFormat The expected format of the value being parsed\n   *     (type is implementation-dependent).\n   */\n  parseTime(value, parseFormat) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Adds an amount of seconds to the specified date.\n   * @param date Date to which to add the seconds.\n   * @param amount Amount of seconds to add to the date.\n   */\n  addSeconds(date, amount) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Compares the time values of two dates.\n   * @param first First date to compare.\n   * @param second Second date to compare.\n   * @returns 0 if the times are equal, a number less than 0 if the first time is earlier,\n   *     a number greater than 0 if the first time is later.\n   */\n  compareTime(first, second) {\n    return this.getHours(first) - this.getHours(second) || this.getMinutes(first) - this.getMinutes(second) || this.getSeconds(first) - this.getSeconds(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Checks if the times of two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the times of the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameTime(first, second) {\n    if (first && second) {\n      const firstValid = this.isValid(first);\n      const secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareTime(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings with an out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/**\n * Matches a time string. Supported formats:\n * - {{hours}}:{{minutes}}\n * - {{hours}}:{{minutes}}:{{seconds}}\n * - {{hours}}:{{minutes}} AM/PM\n * - {{hours}}:{{minutes}}:{{seconds}} AM/PM\n * - {{hours}}.{{minutes}}\n * - {{hours}}.{{minutes}}.{{seconds}}\n * - {{hours}}.{{minutes}} AM/PM\n * - {{hours}}.{{minutes}}.{{seconds}} AM/PM\n */\nconst TIME_REGEX = /^(\\d?\\d)[:.](\\d?\\d)(?:[:.](\\d?\\d))?\\s*(AM|PM)?$/i;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  useUtcForDisplay = false;\n  /** The injected locale. */\n  _matDateLocale = inject(MAT_DATE_LOCALE, {\n    optional: true\n  });\n  constructor() {\n    super();\n    const matDateLocale = inject(MAT_DATE_LOCALE, {\n      optional: true\n    });\n    if (matDateLocale !== undefined) {\n      this._matDateLocale = matDateLocale;\n    }\n    super.setLocale(this._matDateLocale);\n  }\n  getYear(date) {\n    return date.getFullYear();\n  }\n  getMonth(date) {\n    return date.getMonth();\n  }\n  getDate(date) {\n    return date.getDate();\n  }\n  getDayOfWeek(date) {\n    return date.getDay();\n  }\n  getMonthNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      month: style,\n      timeZone: 'utc'\n    });\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n  getDateNames() {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getDayOfWeekNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      weekday: style,\n      timeZone: 'utc'\n    });\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getYearName(date) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      year: 'numeric',\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  getFirstDayOfWeek() {\n    // At the time of writing `Intl.Locale` isn't available\n    // in the internal types so we need to cast to `any`.\n    if (typeof Intl !== 'undefined' && Intl.Locale) {\n      const locale = new Intl.Locale(this.locale);\n      // Some browsers implement a `getWeekInfo` method while others have a `weekInfo` getter.\n      // Note that this isn't supported in all browsers so we need to null check it.\n      const firstDay = (locale.getWeekInfo?.() || locale.weekInfo)?.firstDay ?? 0;\n      // `weekInfo.firstDay` is a number between 1 and 7 where, starting from Monday,\n      // whereas our representation is 0 to 6 where 0 is Sunday so we need to normalize it.\n      return firstDay === 7 ? 0 : firstDay;\n    }\n    // Default to Sunday if the browser doesn't provide the week information.\n    return 0;\n  }\n  getNumDaysInMonth(date) {\n    return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n  }\n  clone(date) {\n    return new Date(date.getTime());\n  }\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n    return result;\n  }\n  today() {\n    return new Date();\n  }\n  parse(value, parseFormat) {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      ...displayFormat,\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  addCalendarYears(date, years) {\n    return this.addCalendarMonths(date, years * 12);\n  }\n  addCalendarMonths(date, months) {\n    let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n    return newDate;\n  }\n  addCalendarDays(date, days) {\n    return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n  }\n  toIso8601(date) {\n    return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return obj instanceof Date;\n  }\n  isValid(date) {\n    return !isNaN(date.getTime());\n  }\n  invalid() {\n    return new Date(NaN);\n  }\n  setTime(target, hours, minutes, seconds) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!inRange(hours, 0, 23)) {\n        throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n      }\n      if (!inRange(minutes, 0, 59)) {\n        throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n      }\n      if (!inRange(seconds, 0, 59)) {\n        throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n      }\n    }\n    const clone = this.clone(target);\n    clone.setHours(hours, minutes, seconds, 0);\n    return clone;\n  }\n  getHours(date) {\n    return date.getHours();\n  }\n  getMinutes(date) {\n    return date.getMinutes();\n  }\n  getSeconds(date) {\n    return date.getSeconds();\n  }\n  parseTime(userValue, parseFormat) {\n    if (typeof userValue !== 'string') {\n      return userValue instanceof Date ? new Date(userValue.getTime()) : null;\n    }\n    const value = userValue.trim();\n    if (value.length === 0) {\n      return null;\n    }\n    // Attempt to parse the value directly.\n    let result = this._parseTimeString(value);\n    // Some locales add extra characters around the time, but are otherwise parseable\n    // (e.g. `00:05 ч.` in bg-BG). Try replacing all non-number and non-colon characters.\n    if (result === null) {\n      const withoutExtras = value.replace(/[^0-9:(AM|PM)]/gi, '').trim();\n      if (withoutExtras.length > 0) {\n        result = this._parseTimeString(withoutExtras);\n      }\n    }\n    return result || this.invalid();\n  }\n  addSeconds(date, amount) {\n    return new Date(date.getTime() + amount * 1000);\n  }\n  /** Creates a date but allows the month and date to overflow. */\n  _createDateWithOverflow(year, month, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  _2digit(n) {\n    return ('00' + n).slice(-2);\n  }\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  _format(dtf, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n  /**\n   * Attempts to parse a time string into a date object. Returns null if it cannot be parsed.\n   * @param value Time string to parse.\n   */\n  _parseTimeString(value) {\n    // Note: we can technically rely on the browser for the time parsing by generating\n    // an ISO string and appending the string to the end of it. We don't do it, because\n    // browsers aren't consistent in what they support. Some examples:\n    // - Safari doesn't support AM/PM.\n    // - Firefox produces a valid date object if the time string has overflows (e.g. 12:75) while\n    //   other browsers produce an invalid date.\n    // - Safari doesn't allow padded numbers.\n    const parsed = value.toUpperCase().match(TIME_REGEX);\n    if (parsed) {\n      let hours = parseInt(parsed[1]);\n      const minutes = parseInt(parsed[2]);\n      let seconds = parsed[3] == null ? undefined : parseInt(parsed[3]);\n      const amPm = parsed[4];\n      if (hours === 12) {\n        hours = amPm === 'AM' ? 0 : hours;\n      } else if (amPm === 'PM') {\n        hours += 12;\n      }\n      if (inRange(hours, 0, 23) && inRange(minutes, 0, 59) && (seconds == null || inRange(seconds, 0, 59))) {\n        return this.setTime(this.today(), hours, minutes, seconds || 0);\n      }\n    }\n    return null;\n  }\n  static ɵfac = function NativeDateAdapter_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NativeDateAdapter)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NativeDateAdapter,\n    factory: NativeDateAdapter.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateAdapter, [{\n    type: Injectable\n  }], () => [], null);\n})();\n/** Checks whether a number is within a certain range. */\nfunction inRange(value, min, max) {\n  return !isNaN(value) && value >= min && value <= max;\n}\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null,\n    timeInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    timeInput: {\n      hour: 'numeric',\n      minute: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    },\n    timeOptionLabel: {\n      hour: 'numeric',\n      minute: 'numeric'\n    }\n  }\n};\nclass NativeDateModule {\n  static ɵfac = function NativeDateModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NativeDateModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NativeDateModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: DateAdapter,\n      useClass: NativeDateAdapter\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    }]\n  }], null, null);\n})();\nclass MatNativeDateModule {\n  static ɵfac = function MatNativeDateModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatNativeDateModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatNativeDateModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideNativeDateAdapter()]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [provideNativeDateAdapter()]\n    }]\n  }], null, null);\n})();\nfunction provideNativeDateAdapter(formats = MAT_NATIVE_DATE_FORMATS) {\n  return [{\n    provide: DateAdapter,\n    useClass: NativeDateAdapter\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: formats\n  }];\n}\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n  static ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ShowOnDirtyErrorStateMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ShowOnDirtyErrorStateMatcher,\n    factory: ShowOnDirtyErrorStateMatcher.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n  static ɵfac = function ErrorStateMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ErrorStateMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ErrorStateMatcher,\n    factory: ErrorStateMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {\n  static ɵfac = function _StructuralStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _StructuralStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _StructuralStylesLoader,\n    selectors: [[\"structural-styles\"]],\n    decls: 0,\n    vars: 0,\n    template: function _StructuralStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_StructuralStylesLoader, [{\n    type: Component,\n    args: [{\n      selector: 'structural-styles',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n  static ɵfac = function MatLine_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatLine)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatLine,\n    selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n    hostAttrs: [1, \"mat-line\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLine, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-line], [matLine]',\n      host: {\n        'class': 'mat-line'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n  static ɵfac = function MatLineModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatLineModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatLineModule,\n    imports: [MatCommonModule, MatLine],\n    exports: [MatLine, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLineModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatLine],\n      exports: [MatLine, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  _renderer;\n  element;\n  config;\n  _animationForciblyDisabledThroughCss;\n  /** Current state of the ripple. */\n  state = RippleState.HIDDEN;\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  _events = new Map();\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n  /** Event handler that is bound and which dispatches the events to the different targets. */\n  _delegateEventHandler = event => {\n    const target = _getEventTarget(event);\n    if (target) {\n      this._events.get(event.type)?.forEach((handlers, element) => {\n        if (element === target || element.contains(target)) {\n          handlers.forEach(handler => handler.handleEvent(event));\n        }\n      });\n    }\n  };\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nclass _MatRippleStylesLoader {\n  static ɵfac = function _MatRippleStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatRippleStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _MatRippleStylesLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"mat-ripple-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _MatRippleStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatRippleStylesLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'mat-ripple-style-loader': ''\n      },\n      styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\"]\n    }]\n  }], null, null);\n})();\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  _target;\n  _ngZone;\n  _platform;\n  /** Element where the ripples are being added to. */\n  _containerElement;\n  /** Element which triggers the ripple elements on mouse events. */\n  _triggerElement;\n  /** Whether the pointer is currently down or not. */\n  _isPointerDown = false;\n  /**\n   * Map of currently active ripple references.\n   * The ripple reference is mapped to its element event listeners.\n   * The reason why `| null` is used is that event listeners are added only\n   * when the condition is truthy (see the `_startFadeOutTransition` method).\n   */\n  _activeRipples = new Map();\n  /** Latest non-persistent ripple that was triggered. */\n  _mostRecentTransientRipple;\n  /** Time in milliseconds when the last touchstart event happened. */\n  _lastTouchStartEvent;\n  /** Whether pointer-up event listeners have been registered. */\n  _pointerUpEventsRegistered = false;\n  /**\n   * Cached dimensions of the ripple container. Set when the first\n   * ripple is shown and cleared once no more ripples are visible.\n   */\n  _containerRect;\n  static _eventManager = new RippleEventManager();\n  constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n    if (injector) {\n      injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => {\n          // Clear the fallback timer since the transition fired correctly.\n          if (eventListeners) {\n            eventListeners.fallbackTimer = null;\n          }\n          clearTimeout(fallbackTimer);\n          this._finishRippleTransition(rippleRef);\n        };\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        // In some cases where there's a higher load on the browser, it can choose not to dispatch\n        // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n        // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n        // because timers aren't precise. Note that another approach can be to transition the ripple\n        // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n        // `transitionstart`. We go with the timer because it's one less event listener and\n        // it's less likely to break existing tests.\n        const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel,\n          fallbackTimer\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n      if (eventListeners.fallbackTimer !== null) {\n        clearTimeout(eventListeners.fallbackTimer);\n      }\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  _elementRef = inject(ElementRef);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  /** Custom color for all ripples. */\n  color;\n  /** Whether the ripples should be visible outside the component's bounds. */\n  unbounded;\n  /**\n   * Whether the ripple always originates from the center of the host element's bounds, rather\n   * than originating from the location of the click event.\n   */\n  centered;\n  /**\n   * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n   * will be the distance from the center of the ripple to the furthest corner of the host element's\n   * bounding rectangle.\n   */\n  radius = 0;\n  /**\n   * Configuration for the ripple animation. Allows modifying the enter and exit animation\n   * duration of the ripples. The animation durations will be overwritten if the\n   * `NoopAnimationsModule` is being used.\n   */\n  animation;\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  _disabled = false;\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  _trigger;\n  /** Renderer for the ripple DOM manipulations. */\n  _rippleRenderer;\n  /** Options that are set globally for all ripples. */\n  _globalOptions;\n  /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n  _isInitialized = false;\n  constructor() {\n    const ngZone = inject(NgZone);\n    const platform = inject(Platform);\n    const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const injector = inject(Injector);\n    // Note: cannot use `inject()` here, because this class\n    // gets instantiated manually in the ripple loader.\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n  static ɵfac = function MatRipple_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRipple)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatRipple,\n    selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n    hostAttrs: [1, \"mat-ripple\"],\n    hostVars: 2,\n    hostBindings: function MatRipple_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n      }\n    },\n    inputs: {\n      color: [0, \"matRippleColor\", \"color\"],\n      unbounded: [0, \"matRippleUnbounded\", \"unbounded\"],\n      centered: [0, \"matRippleCentered\", \"centered\"],\n      radius: [0, \"matRippleRadius\", \"radius\"],\n      animation: [0, \"matRippleAnimation\", \"animation\"],\n      disabled: [0, \"matRippleDisabled\", \"disabled\"],\n      trigger: [0, \"matRippleTrigger\", \"trigger\"]\n    },\n    exportAs: [\"matRipple\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nclass MatRippleModule {\n  static ɵfac = function MatRippleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatRippleModule,\n    imports: [MatCommonModule, MatRipple],\n    exports: [MatRipple, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatRipple, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  /** Display state of the checkbox. */\n  state = 'unchecked';\n  /** Whether the checkbox is disabled. */\n  disabled = false;\n  /**\n   * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n   * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n   */\n  appearance = 'full';\n  constructor() {}\n  static ɵfac = function MatPseudoCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPseudoCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatPseudoCheckbox,\n    selectors: [[\"mat-pseudo-checkbox\"]],\n    hostAttrs: [1, \"mat-pseudo-checkbox\"],\n    hostVars: 12,\n    hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      }\n    },\n    inputs: {\n      state: \"state\",\n      disabled: \"disabled\",\n      appearance: \"appearance\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatPseudoCheckbox_Template(rf, ctx) {},\n    styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"]\n    }]\n  }], () => [], {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\nclass MatPseudoCheckboxModule {\n  static ɵfac = function MatPseudoCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPseudoCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatPseudoCheckboxModule,\n    imports: [MatCommonModule, MatPseudoCheckbox],\n    exports: [MatPseudoCheckbox]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n  /** Label for the option group. */\n  label;\n  /** whether the option group is disabled. */\n  disabled = false;\n  /** Unique id for the underlying label. */\n  _labelId = inject(_IdGenerator).getId('mat-optgroup-label-');\n  /** Whether the group is in inert a11y mode. */\n  _inert;\n  constructor() {\n    const parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n      optional: true\n    });\n    this._inert = parent?.inertGroups ?? false;\n  }\n  static ɵfac = function MatOptgroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOptgroup)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOptgroup,\n    selectors: [[\"mat-optgroup\"]],\n    hostAttrs: [1, \"mat-mdc-optgroup\"],\n    hostVars: 3,\n    hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n      }\n    },\n    inputs: {\n      label: \"label\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    exportAs: [\"matOptgroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTGROUP,\n      useExisting: MatOptgroup\n    }])],\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 4,\n    consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n    template: function MatOptgroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵprojection(4, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n        i0.ɵɵproperty(\"id\", ctx._labelId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n      }\n    },\n    styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\"]\n    }]\n  }], () => [], {\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  source;\n  isUserInput;\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n  _element = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n    optional: true\n  });\n  group = inject(MAT_OPTGROUP, {\n    optional: true\n  });\n  _signalDisableRipple = false;\n  _selected = false;\n  _active = false;\n  _disabled = false;\n  _mostRecentViewValue = '';\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** The form value of the option. */\n  value;\n  /** The unique ID of the option. */\n  id = inject(_IdGenerator).getId('mat-option-');\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return this._signalDisableRipple ? this._parent.disableRipple() : !!this._parent?.disableRipple;\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  /** Event emitted when the option is selected or deselected. */\n  // tslint:disable-next-line:no-output-on-prefix\n  onSelectionChange = new EventEmitter();\n  /** Element containing the option's text. */\n  _text;\n  /** Emits when the state of the option changes and any parents have to be notified. */\n  _stateChanges = new Subject();\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n  static ɵfac = function MatOption_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOption)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOption,\n    selectors: [[\"mat-option\"]],\n    viewQuery: function MatOption_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n    hostVars: 11,\n    hostBindings: function MatOption_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n          return ctx._selectViaInteraction();\n        })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n        i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      value: \"value\",\n      id: \"id\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      onSelectionChange: \"onSelectionChange\"\n    },\n    exportAs: [\"matOption\"],\n    ngContentSelectors: _c4,\n    decls: 8,\n    vars: 5,\n    consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n    template: function MatOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵtemplate(0, MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 2, 0);\n        i0.ɵɵprojection(4, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(6, MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n        i0.ɵɵelement(7, \"div\", 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.multiple ? 0 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵconditional(!ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.group && ctx.group._inert ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatPseudoCheckbox, MatRipple],\n    styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatPseudoCheckbox, MatRipple],\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [], {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nclass MatOptionModule {\n  static ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOptionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatOptionModule,\n    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n    exports: [MatOption, MatOptgroup]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  _document = inject(DOCUMENT);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _eventCleanups;\n  _hosts = new Map();\n  constructor() {\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => {\n      return rippleInteractionEvents.map(name => _bindEventWithOptions(renderer, this._document, name, this._onInteraction, eventListenerOptions));\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.target.rippleDisabled = disabled;\n      if (!disabled && !ripple.hasSetUpEvents) {\n        ripple.hasSetUpEvents = true;\n        ripple.renderer.setupTriggerEvents(host);\n      }\n    } else if (disabled) {\n      // Otherwise, set an attribute so we know what the\n      // disabled state should be when the ripple is initialized.\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /**\n   * Handles creating and attaching component internals\n   * when a component is initially interacted with.\n   */\n  _onInteraction = event => {\n    const eventTarget = _getEventTarget(event);\n    if (eventTarget instanceof HTMLElement) {\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n      if (element) {\n        this._createRipple(element);\n      }\n    }\n  };\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document || this._hosts.has(host)) {\n      return;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    const isNoopAnimations = this._animationMode === 'NoopAnimations';\n    const globalOptions = this._globalRippleOptions;\n    const enterDuration = isNoopAnimations ? 0 : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n    const exitDuration = isNoopAnimations ? 0 : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n    const target = {\n      rippleDisabled: isNoopAnimations || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n      rippleConfig: {\n        centered: host.hasAttribute(matRippleCentered),\n        terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n        animation: {\n          enterDuration,\n          exitDuration\n        }\n      }\n    };\n    const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n    const hasSetUpEvents = !target.rippleDisabled;\n    if (hasSetUpEvents) {\n      renderer.setupTriggerEvents(host);\n    }\n    this._hosts.set(host, {\n      target,\n      renderer,\n      hasSetUpEvents\n    });\n    host.removeAttribute(matRippleUninitialized);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      ripple.renderer._removeTriggerEvents();\n      this._hosts.delete(host);\n    }\n  }\n  static ɵfac = function MatRippleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatRippleLoader,\n    factory: MatRippleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n  /** Position of the label relative to the content. */\n  labelPosition;\n  static ɵfac = function _MatInternalFormField_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatInternalFormField)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _MatInternalFormField,\n    selectors: [[\"div\", \"mat-internal-form-field\", \"\"]],\n    hostAttrs: [1, \"mdc-form-field\", \"mat-internal-form-field\"],\n    hostVars: 2,\n    hostBindings: function _MatInternalFormField_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition === \"before\");\n      }\n    },\n    inputs: {\n      labelPosition: \"labelPosition\"\n    },\n    attrs: _c5,\n    ngContentSelectors: _c6,\n    decls: 1,\n    vars: 0,\n    template: function _MatInternalFormField_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatInternalFormField, [{\n    type: Component,\n    args: [{\n      selector: 'div[mat-internal-form-field]',\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mdc-form-field mat-internal-form-field',\n        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"'\n      },\n      styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\"]\n    }]\n  }], null, {\n    labelPosition: [{\n      type: Input,\n      args: [{\n        required: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleLoader, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, RippleState, ShowOnDirtyErrorStateMatcher, VERSION, _ErrorStateTracker, _MatInternalFormField, _StructuralStylesLoader, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, provideNativeDateAdapter, setLines };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,YAAY;AAClB,IAAM,MAAM;AAEZ,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,MAAM;AAGZ,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,aAAa;AAInB,IAAM,SAAS;AACf,IAAM,OAAO;AASb,IAAM,OAAO;AAKb,IAAM,IAAI;AAyBV,IAAM,IAAI;AACV,IAAM,OAAO;AAsDb,IAAM,WAAW;AAMjB,SAAS,eAAe,UAAU,WAAW;AAC3C,MAAI,UAAU,QAAQ;AACpB,WAAO,UAAU,KAAK,cAAY,MAAM,QAAQ,CAAC;AAAA,EACnD;AACA,SAAO,MAAM,UAAU,MAAM,YAAY,MAAM,WAAW,MAAM;AAClE;;;AC3HA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,CAAC,aAAa,IAAI,GAAG;AACvB,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;;;ACDA,SAAS,mBAAmB,QAAQ;AAElC,MAAI,OAAO,SAAS,mBAAmB,OAAO,kBAAkB,SAAS;AACvE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,aAAa;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,WAAW,QAAQ,KAAK;AACjD,UAAI,EAAE,OAAO,WAAW,CAAC,aAAa,UAAU;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,aAAa,QAAQ,KAAK;AACnD,UAAI,EAAE,OAAO,aAAa,CAAC,aAAa,UAAU;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,qBAAqB,cAAc,OAAO,IAAI,iBAAiB,QAAQ;AAAA,EACvF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,IACjC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,2BAA2B,OAAO,uBAAuB;AAAA;AAAA,EAEzD,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,kBAAkB,QAAQ,CAAC,GAAG,YAAY,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC/E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,SAAS,KAAK,gBAAgB,OAAO;AAC3C,YAAM,eAAe,OAAO,KAAK,IAAI,aAAW,QAAQ,OAAO,YAAU,CAAC,mBAAmB,MAAM,CAAC,CAAC,GAAG,OAAO,aAAW,CAAC,CAAC,QAAQ,MAAM,CAAC,EAAE,UAAU,aAAW;AAChK,aAAK,QAAQ,IAAI,MAAM;AACrB,mBAAS,KAAK,OAAO;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AACD,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,kBAAkB,OAAO;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS;AACvB,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACxC,cAAM,SAAS,IAAI,QAAQ;AAC3B,cAAM,WAAW,KAAK,yBAAyB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACzF,YAAI,UAAU;AACZ,mBAAS,QAAQ,SAAS;AAAA,YACxB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AACA,aAAK,kBAAkB,IAAI,SAAS;AAAA,UAClC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,aAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,MACtC;AACA,aAAO,KAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACzB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,WAAK,kBAAkB,IAAI,OAAO,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,EAAE,OAAO;AAC9C,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,kBAAkB,IAAI,OAAO;AACtC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,kBAAkB,OAAO,OAAO;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,mBAAmB,OAAO,eAAe;AAAA,EACzC,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,QAAQ,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,EACzD;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,qBAAqB,KAAK;AAC3C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,qBAAqB;AACnB,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,UAAU;AAChD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,aAAa;AAClB,UAAM,SAAS,KAAK,iBAAiB,QAAQ,KAAK,WAAW;AAC7D,SAAK,wBAAwB,KAAK,WAAW,OAAO,KAAK,aAAa,KAAK,QAAQ,CAAC,IAAI,QAAQ,UAAU,KAAK,KAAK;AAAA,EACtH;AAAA,EACA,eAAe;AACb,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,6BAA6B,YAAY,gBAAgB;AAAA,MACvE,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB;AAAA,IAC3B,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,uBAAuB;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,MAC3B,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AChQH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,qCAAqC,oBAAI,IAAI;AAEnD,IAAI;AAEJ,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,OAAO,QAAQ;AAAA,EAC3B,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,KAAK,UAAU,aAAa,OAAO;AAAA;AAAA;AAAA,MAGtD,OAAO,WAAW,KAAK,MAAM;AAAA,QAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO;AACjD,2BAAqB,OAAO,KAAK,MAAM;AAAA,IACzC;AACA,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,SAAS,qBAAqB,OAAO,OAAO;AAC1C,MAAI,mCAAmC,IAAI,KAAK,GAAG;AACjD;AAAA,EACF;AACA,MAAI;AACF,QAAI,CAAC,qBAAqB;AACxB,4BAAsB,SAAS,cAAc,OAAO;AACpD,UAAI,OAAO;AACT,4BAAoB,aAAa,SAAS,KAAK;AAAA,MACjD;AACA,0BAAoB,aAAa,QAAQ,UAAU;AACnD,eAAS,KAAK,YAAY,mBAAmB;AAAA,IAC/C;AACA,QAAI,oBAAoB,OAAO;AAC7B,0BAAoB,MAAM,WAAW,UAAU,KAAK,cAAc,CAAC;AACnE,yCAAmC,IAAI,KAAK;AAAA,IAC9C;AAAA,EACF,SAAS,GAAG;AACV,YAAQ,MAAM,CAAC;AAAA,EACjB;AACF;AAEA,SAAS,eAAe,OAAO;AAG7B,SAAO;AAAA,IACL,SAAS,UAAU,SAAS,UAAU;AAAA,IACtC,OAAO;AAAA,IACP,aAAa,MAAM;AAAA,IAAC;AAAA,IACpB,gBAAgB,MAAM;AAAA,IAAC;AAAA,EACzB;AACF;AAGA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,gBAAgB,OAAO,YAAY;AAAA,EACnC,QAAQ,OAAO,MAAM;AAAA;AAAA,EAErB,WAAW,oBAAI,IAAI;AAAA;AAAA,EAEnB,kBAAkB,IAAI,QAAQ;AAAA,EAC9B,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,cAAc;AACZ,SAAK,gBAAgB,KAAK;AAC1B,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,OAAO;AACf,UAAM,UAAU,aAAa,YAAY,KAAK,CAAC;AAC/C,WAAO,QAAQ,KAAK,gBAAc,KAAK,eAAe,UAAU,EAAE,IAAI,OAAO;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AACb,UAAM,UAAU,aAAa,YAAY,KAAK,CAAC;AAC/C,UAAM,cAAc,QAAQ,IAAI,WAAS,KAAK,eAAe,KAAK,EAAE,UAAU;AAC9E,QAAI,kBAAkB,cAAc,WAAW;AAE/C,sBAAkB,OAAO,gBAAgB,KAAK,KAAK,CAAC,CAAC,GAAG,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;AACtG,WAAO,gBAAgB,KAAK,IAAI,sBAAoB;AAClD,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,aAAa,CAAC;AAAA,MAChB;AACA,uBAAiB,QAAQ,CAAC;AAAA,QACxB;AAAA,QACA;AAAA,MACF,MAAM;AACJ,iBAAS,UAAU,SAAS,WAAW;AACvC,iBAAS,YAAY,KAAK,IAAI;AAAA,MAChC,CAAC;AACD,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,eAAe,OAAO;AAEpB,QAAI,KAAK,SAAS,IAAI,KAAK,GAAG;AAC5B,aAAO,KAAK,SAAS,IAAI,KAAK;AAAA,IAChC;AACA,UAAM,MAAM,KAAK,cAAc,WAAW,KAAK;AAE/C,UAAM,kBAAkB,IAAI,WAAW,cAAY;AAMjD,YAAM,UAAU,OAAK,KAAK,MAAM,IAAI,MAAM,SAAS,KAAK,CAAC,CAAC;AAC1D,UAAI,YAAY,OAAO;AACvB,aAAO,MAAM;AACX,YAAI,eAAe,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC,EAAE,KAAK,UAAU,GAAG,GAAG,IAAI,CAAC;AAAA,MAC3B;AAAA,IACF,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,EAAE,GAAG,UAAU,KAAK,eAAe,CAAC;AAEpC,UAAM,SAAS;AAAA,MACb,YAAY;AAAA,MACZ;AAAA,IACF;AACA,SAAK,SAAS,IAAI,OAAO,MAAM;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,IAAI,WAAS,MAAM,KAAK,CAAC;AAC3G;AAIA,IAAM,cAAc;AAAA,EAClB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAChB;;;AC9NA,IAAM,eAAe;AAKrB,SAAS,oBAAoB,IAAI,MAAM,IAAI;AACzC,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,MAAI,IAAI,KAAK,gBAAc,WAAW,KAAK,MAAM,EAAE,GAAG;AACpD;AAAA,EACF;AACA,MAAI,KAAK,EAAE;AACX,KAAG,aAAa,MAAM,IAAI,KAAK,YAAY,CAAC;AAC9C;AAKA,SAAS,uBAAuB,IAAI,MAAM,IAAI;AAC5C,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,QAAM,cAAc,IAAI,OAAO,SAAO,QAAQ,EAAE;AAChD,MAAI,YAAY,QAAQ;AACtB,OAAG,aAAa,MAAM,YAAY,KAAK,YAAY,CAAC;AAAA,EACtD,OAAO;AACL,OAAG,gBAAgB,IAAI;AAAA,EACzB;AACF;AAKA,SAAS,oBAAoB,IAAI,MAAM;AAErC,QAAM,YAAY,GAAG,aAAa,IAAI;AACtC,SAAO,WAAW,MAAM,MAAM,KAAK,CAAC;AACtC;AAaA,IAAM,4BAA4B;AAMlC,IAAM,iCAAiC;AAEvC,IAAI,SAAS;AAMb,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,mBAAmB,oBAAI,IAAI;AAAA;AAAA,EAE3B,qBAAqB;AAAA;AAAA,EAErB,MAAM,GAAG,QAAQ;AAAA,EACjB,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,SAAK,MAAM,OAAO,MAAM,IAAI,MAAM;AAAA,EACpC;AAAA,EACA,SAAS,aAAa,SAAS,MAAM;AACnC,QAAI,CAAC,KAAK,gBAAgB,aAAa,OAAO,GAAG;AAC/C;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,OAAO,YAAY,UAAU;AAE/B,mBAAa,SAAS,KAAK,GAAG;AAC9B,WAAK,iBAAiB,IAAI,KAAK;AAAA,QAC7B,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,CAAC,KAAK,iBAAiB,IAAI,GAAG,GAAG;AAC1C,WAAK,sBAAsB,SAAS,IAAI;AAAA,IAC1C;AACA,QAAI,CAAC,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACxD,WAAK,qBAAqB,aAAa,GAAG;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB,aAAa,SAAS,MAAM;AAC5C,QAAI,CAAC,WAAW,CAAC,KAAK,eAAe,WAAW,GAAG;AACjD;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACvD,WAAK,wBAAwB,aAAa,GAAG;AAAA,IAC/C;AAGA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAI,qBAAqB,kBAAkB,mBAAmB,GAAG;AAC/D,aAAK,sBAAsB,GAAG;AAAA,MAChC;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB,WAAW,WAAW,GAAG;AACpD,WAAK,mBAAmB,OAAO;AAC/B,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,oBAAoB,KAAK,UAAU,iBAAiB,IAAI,8BAA8B,KAAK,KAAK,GAAG,IAAI;AAC7G,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,WAAK,kCAAkC,kBAAkB,CAAC,CAAC;AAC3D,wBAAkB,CAAC,EAAE,gBAAgB,8BAA8B;AAAA,IACrE;AACA,SAAK,oBAAoB,OAAO;AAChC,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS,MAAM;AACnC,UAAM,iBAAiB,KAAK,UAAU,cAAc,KAAK;AACzD,iBAAa,gBAAgB,KAAK,GAAG;AACrC,mBAAe,cAAc;AAC7B,QAAI,MAAM;AACR,qBAAe,aAAa,QAAQ,IAAI;AAAA,IAC1C;AACA,SAAK,yBAAyB;AAC9B,SAAK,mBAAmB,YAAY,cAAc;AAClD,SAAK,iBAAiB,IAAI,OAAO,SAAS,IAAI,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB,KAAK;AACzB,SAAK,iBAAiB,IAAI,GAAG,GAAG,gBAAgB,OAAO;AACvD,SAAK,iBAAiB,OAAO,GAAG;AAAA,EAClC;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,UAAM,qBAAqB;AAC3B,UAAM,mBAAmB,KAAK,UAAU,iBAAiB,IAAI,kBAAkB,qBAAqB;AACpG,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAKhD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,UAAM,oBAAoB,KAAK,UAAU,cAAc,KAAK;AAK5D,sBAAkB,MAAM,aAAa;AAGrC,sBAAkB,UAAU,IAAI,kBAAkB;AAClD,sBAAkB,UAAU,IAAI,qBAAqB;AACrD,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,wBAAkB,aAAa,YAAY,QAAQ;AAAA,IACrD;AACA,SAAK,UAAU,KAAK,YAAY,iBAAiB;AACjD,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,kCAAkC,SAAS;AAEzC,UAAM,uBAAuB,oBAAoB,SAAS,kBAAkB,EAAE,OAAO,QAAM,GAAG,QAAQ,yBAAyB,KAAK,CAAC;AACrI,YAAQ,aAAa,oBAAoB,qBAAqB,KAAK,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS,KAAK;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AAGvD,wBAAoB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACpF,YAAQ,aAAa,gCAAgC,KAAK,GAAG;AAC7D,sBAAkB;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,SAAS,KAAK;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,sBAAkB;AAClB,2BAAuB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACvF,YAAQ,gBAAgB,8BAA8B;AAAA,EACxD;AAAA;AAAA,EAEA,6BAA6B,SAAS,KAAK;AACzC,UAAM,eAAe,oBAAoB,SAAS,kBAAkB;AACpE,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAM,YAAY,qBAAqB,kBAAkB,eAAe;AACxE,WAAO,CAAC,CAAC,aAAa,aAAa,QAAQ,SAAS,KAAK;AAAA,EAC3D;AAAA;AAAA,EAEA,gBAAgB,SAAS,SAAS;AAChC,QAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,WAAW,OAAO,YAAY,UAAU;AAI1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,WAAW,OAAO,KAAK,GAAG,OAAO,GAAG,KAAK;AAChE,UAAM,YAAY,QAAQ,aAAa,YAAY;AAGnD,WAAO,iBAAiB,CAAC,aAAa,UAAU,KAAK,MAAM,iBAAiB;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,WAAO,QAAQ,aAAa,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,SAAS,OAAO,SAAS,MAAM;AAC7B,SAAO,OAAO,YAAY,WAAW,GAAG,QAAQ,EAAE,IAAI,OAAO,KAAK;AACpE;AAEA,SAAS,aAAa,SAAS,WAAW;AACxC,MAAI,CAAC,QAAQ,IAAI;AACf,YAAQ,KAAK,GAAG,yBAAyB,IAAI,SAAS,IAAI,QAAQ;AAAA,EACpE;AACF;AACA,IAAM,yCAAyC;AAK/C,IAAM,YAAN,MAAgB;AAAA,EACd,mBAAmB,IAAI,QAAQ;AAAA,EAC/B,SAAS,CAAC;AAAA,EACV,qBAAqB;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA,EACnB;AAAA,EACA,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,eAAe,KAAK;AAAA,EACpB,YAAY,cAAc,QAAQ;AAChC,UAAM,oBAAoB,OAAO,QAAQ,qBAAqB,WAAW,OAAO,mBAAmB;AACnG,QAAI,QAAQ,eAAe;AACzB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,SAAK,OAAO,cAAc,eAAe,cAAc,aAAa,UAAU,aAAa,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC5I,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AAGtB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG;AACvC,WAAK,iBAAiB,KAAK,MAAM,IAAI,kBAAkB,CAAC;AAAA,IAC1D,WAAW,WAAW,KAAK,WAAW,KAAK,WAAW,QAAQ,WAAW,MAAM;AAC7E,WAAK,iBAAiB,KAAK,OAAO,aAAa,OAAO,CAAC;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,iBAAiB,mBAAmB;AAIlC,SAAK,iBAAiB,KAAK,IAAI,YAAU,KAAK,gBAAgB,KAAK,MAAM,CAAC,GAAG,aAAa,iBAAiB,GAAG,OAAO,MAAM,KAAK,gBAAgB,SAAS,CAAC,GAAG,IAAI,MAAM,KAAK,gBAAgB,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE,UAAU,iBAAe;AAGlP,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK;AAC/C,cAAM,SAAS,KAAK,qBAAqB,KAAK,KAAK,OAAO;AAC1D,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,CAAC,KAAK,mBAAmB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG;AAC7G,eAAK,cAAc,KAAK,IAAI;AAC5B;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAMA,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,cAAc,OAAO,IAAI;AAAA,EACzB,QAAQ;AAAA,EACR,yBAAyB,aAAa;AAAA,EACtC;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,uBAAuB,CAAC;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,IACf,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAQ,KAAK;AAAA,EAChC,YAAY,QAAQ,UAAU;AAC5B,SAAK,SAAS;AAId,QAAI,kBAAkB,WAAW;AAC/B,WAAK,2BAA2B,OAAO,QAAQ,UAAU,cAAY,KAAK,cAAc,SAAS,QAAQ,CAAC,CAAC;AAAA,IAC7G,WAAW,SAAS,MAAM,GAAG;AAC3B,UAAI,CAAC,aAAa,OAAO,cAAc,eAAe,YAAY;AAChE,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AACA,WAAK,aAAa,OAAO,MAAM,KAAK,cAAc,OAAO,CAAC,GAAG;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,SAAS,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,cAAc,WAAW;AACvB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,aAAa,MAAM;AAC1B,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU,MAAM;AACtC,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,WAAW;AACnC,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,MAAM;AAC5B,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,mBAAmB,KAAK;AACpC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAMA,SAAQ,KAAK,eAAe;AAClC,UAAIA,OAAM,SAAS,KAAKA,OAAM,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC/E,cAAM,MAAM,8EAA8E;AAAA,MAC5F;AAAA,IACF;AACA,SAAK,uBAAuB,YAAY;AACxC,UAAM,QAAQ,KAAK,eAAe;AAClC,SAAK,aAAa,IAAI,UAAU,OAAO;AAAA,MACrC,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,cAAc,IAAI;AAAA,IACzB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,YAAY,MAAM;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,UAAU,MAAM;AAC7B,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU,MAAM,QAAQ,IAAI;AACzC,SAAK,iBAAiB;AAAA,MACpB;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,qBAAqB,KAAK,YAAY;AAC5C,SAAK,iBAAiB,IAAI;AAC1B,QAAI,KAAK,YAAY,MAAM,oBAAoB;AAC7C,WAAK,OAAO,KAAK,KAAK,gBAAgB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AACtB,UAAM,YAAY,CAAC,UAAU,WAAW,WAAW,UAAU;AAC7D,UAAM,oBAAoB,UAAU,MAAM,cAAY;AACpD,aAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,qBAAqB,QAAQ,QAAQ,IAAI;AAAA,IAC3E,CAAC;AACD,YAAQ,SAAS;AAAA,MACf,KAAK;AACH,aAAK,OAAO,KAAK;AACjB;AAAA,MACF,KAAK;AACH,YAAI,KAAK,aAAa,mBAAmB;AACvC,eAAK,kBAAkB;AACvB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,aAAa,mBAAmB;AACvC,eAAK,sBAAsB;AAC3B;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,gBAAgB,QAAQ,KAAK,sBAAsB,IAAI,KAAK,kBAAkB;AACnF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,gBAAgB,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB;AACnF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,mBAAmB;AACxB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,kBAAkB;AACvB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,WAAW,mBAAmB;AACpD,gBAAM,cAAc,KAAK,mBAAmB,KAAK,eAAe;AAChE,eAAK,sBAAsB,cAAc,IAAI,cAAc,GAAG,CAAC;AAC/D;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,WAAW,mBAAmB;AACpD,gBAAM,cAAc,KAAK,mBAAmB,KAAK,eAAe;AAChE,gBAAM,cAAc,KAAK,eAAe,EAAE;AAC1C,eAAK,sBAAsB,cAAc,cAAc,cAAc,cAAc,GAAG,EAAE;AACxF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACE,YAAI,qBAAqB,eAAe,OAAO,UAAU,GAAG;AAC1D,eAAK,YAAY,UAAU,KAAK;AAAA,QAClC;AAGA;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,CAAC,CAAC,KAAK,cAAc,KAAK,WAAW,SAAS;AAAA,EACvD;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,sBAAsB,GAAG,CAAC;AAAA,EACjC;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,sBAAsB,KAAK,eAAe,EAAE,SAAS,GAAG,EAAE;AAAA,EACjE;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,mBAAmB,IAAI,KAAK,mBAAmB,IAAI,KAAK,sBAAsB,CAAC;AAAA,EACtF;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,mBAAmB,KAAK,KAAK,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB,EAAE;AAAA,EACpG;AAAA,EACA,iBAAiB,MAAM;AACrB,UAAM,YAAY,KAAK,eAAe;AACtC,UAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,UAAU,QAAQ,IAAI;AACtE,UAAM,aAAa,UAAU,KAAK;AAElC,SAAK,YAAY,IAAI,cAAc,OAAO,OAAO,UAAU;AAC3D,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,0BAA0B,YAAY;AAC3C,SAAK,YAAY,QAAQ;AACzB,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO;AAC3B,SAAK,QAAQ,KAAK,qBAAqB,KAAK,IAAI,KAAK,wBAAwB,KAAK;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AAC1B,UAAM,QAAQ,KAAK,eAAe;AAClC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AACtC,YAAM,SAAS,KAAK,mBAAmB,QAAQ,IAAI,MAAM,UAAU,MAAM;AACzE,YAAM,OAAO,MAAM,KAAK;AACxB,UAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG;AAChC,aAAK,cAAc,KAAK;AACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,OAAO;AAC7B,SAAK,sBAAsB,KAAK,mBAAmB,OAAO,KAAK;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO,eAAe;AAC1C,UAAM,QAAQ,KAAK,eAAe;AAClC,QAAI,CAAC,MAAM,KAAK,GAAG;AACjB;AAAA,IACF;AACA,WAAO,KAAK,iBAAiB,MAAM,KAAK,CAAC,GAAG;AAC1C,eAAS;AACT,UAAI,CAAC,MAAM,KAAK,GAAG;AACjB;AAAA,MACF;AAAA,IACF;AACA,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO,KAAK,kBAAkB,YAAY,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACzE;AAAA;AAAA,EAEA,cAAc,UAAU;AACtB,SAAK,YAAY,SAAS,QAAQ;AAClC,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,YAAY;AACd,YAAM,WAAW,SAAS,QAAQ,UAAU;AAC5C,UAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,aAAK,mBAAmB;AACxB,aAAK,YAAY,4BAA4B,QAAQ;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,6BAAN,cAAyC,eAAe;AAAA,EACtD,cAAc,OAAO;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,kBAAkB;AAAA,IACpC;AACA,UAAM,cAAc,KAAK;AACzB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,gBAAgB;AAAA,IAClC;AAAA,EACF;AACF;AACA,IAAM,kBAAN,cAA8B,eAAe;AAAA,EAC3C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,eAAe,QAAQ;AACrB,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,cAAc,IAAI;AACxB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,MAAM,KAAK,OAAO;AAAA,IACpC;AAAA,EACF;AACF;AAOA,IAAM,iBAAN,MAAqB;AAAA;AAAA,EAEnB,mBAAmB;AAAA;AAAA,EAEnB,cAAc;AAAA;AAAA,EAEd,+BAA+B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,mBAAmB,WAAS;AAAA;AAAA,EAE5B,aAAa,UAAQ;AAAA;AAAA,EAErB,SAAS,CAAC;AAAA,EACV;AAAA,EACA,yBAAyB,aAAa;AAAA,EACtC,qBAAqB;AAAA,EACrB,mBAAmB;AACjB,QAAI,KAAK,sBAAsB,KAAK,OAAO,WAAW,GAAG;AACvD;AAAA,IACF;AACA,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,gBAAgB,KAAK,OAAO,CAAC,CAAC,GAAG;AACnF,sBAAc;AACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,WAAW;AAG1C,QAAI,WAAW,eAAe;AAC5B,WAAK,aAAa,QAAQ;AAC1B,WAAK,mBAAmB;AACxB,WAAK,cAAc;AACnB,WAAK,YAAY,4BAA4B,WAAW;AACxD,iBAAW,cAAc;AAAA,IAC3B,OAAO;AAEL,WAAK,UAAU,WAAW;AAAA,IAC5B;AACA,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,OAAO,QAAQ;AAIzB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,SAAS,MAAM,QAAQ;AAC5B,YAAM,QAAQ,UAAU,cAAY;AAClC,aAAK,SAAS,SAAS,QAAQ;AAC/B,aAAK,YAAY,SAAS,KAAK,MAAM;AACrC,aAAK,uBAAuB,KAAK,MAAM;AACvC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,WAAW,aAAa,KAAK,GAAG;AAC9B,YAAM,UAAU,cAAY;AAC1B,aAAK,SAAS;AACd,aAAK,YAAY,SAAS,QAAQ;AAClC,aAAK,uBAAuB,QAAQ;AACpC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AACd,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,OAAO,OAAO,gCAAgC,WAAW;AAC3D,WAAK,+BAA+B,OAAO;AAAA,IAC7C;AACA,QAAI,OAAO,uBAAuB;AAChC,WAAK,yBAAyB,OAAO;AAAA,IACvC;AACA,QAAI,OAAO,eAAe;AACxB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,QAAI,OAAO,SAAS;AAClB,WAAK,aAAa,OAAO;AAAA,IAC3B;AACA,QAAI,OAAO,OAAO,8BAA8B,aAAa;AAC3D,WAAK,cAAc,OAAO,yBAAyB;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,MAAM,MAAM;AAClB,YAAQ,KAAK;AAAA,MACX,KAAK;AAEH;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,qBAAqB,IAAI,KAAK,mBAAmB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,mBAAmB,IAAI,KAAK,qBAAqB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB;AACrB;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,qBAAqB;AAC1B;AAAA,MACF;AACE,YAAI,MAAM,QAAQ,KAAK;AACrB,eAAK,kCAAkC;AACvC;AAAA,QACF;AACA,aAAK,YAAY,UAAU,KAAK;AAGhC;AAAA,IACJ;AAEA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,KAAK,4BAA4B,EAAE,CAAC;AAAA,EACrD;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,gCAAgC,KAAK,OAAO,MAAM,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,4BAA4B,KAAK,gBAAgB,CAAC;AAAA,EACxE;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,UAAU,KAAK,gCAAgC,KAAK,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU,aAAa,UAAU,CAAC,GAAG;AAEnC,YAAQ,oBAAoB;AAC5B,QAAI,QAAQ,OAAO,gBAAgB,WAAW,cAAc,KAAK,OAAO,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,WAAW,CAAC;AAChJ,QAAI,QAAQ,KAAK,SAAS,KAAK,OAAO,QAAQ;AAC5C;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,KAAK;AAEpC,QAAI,KAAK,gBAAgB,QAAQ,KAAK,WAAW,UAAU,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG;AAClG;AAAA,IACF;AACA,UAAM,qBAAqB,KAAK;AAChC,SAAK,cAAc,cAAc;AACjC,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAClD,SAAK,aAAa,MAAM;AACxB,wBAAoB,QAAQ;AAC5B,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,OAAO,KAAK,KAAK,WAAW;AAAA,IACnC;AACA,QAAI,KAAK,8BAA8B;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,WAAW,SAAS,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,UAAU,CAAC;AACjG,QAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,WAAK,mBAAmB;AACxB,WAAK,YAAY,4BAA4B,QAAQ;AAAA,IACvD;AAAA,EACF;AAAA,EACA,cAAc,kBAAkB;AAC9B,SAAK,aAAa,IAAI,UAAU,KAAK,QAAQ;AAAA,MAC3C,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,UAAU,IAAI;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B,eAAe;AACzC,aAAS,IAAI,gBAAgB,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3D,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC,eAAe;AAC7C,aAAS,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,KAAK,uBAAuB,GAAG;AACjC,WAAK,YAAY,SAAS;AAAA,IAC5B,OAAO;AACL,YAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,UAAI,CAAC,UAAU,KAAK,iBAAiB,MAAM,GAAG;AAC5C;AAAA,MACF;AACA,WAAK,UAAU,MAAM;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,uBAAiB,KAAK,YAAY,YAAY,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,cAAY;AACnF,cAAM,aAAa,SAAS,KAAK,WAAS,CAAC,KAAK,iBAAiB,KAAK,CAAC;AACvE,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AACA,aAAK,UAAU,UAAU;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,YAAY,eAAe,YAAY,KAAK,YAAY,aAAa,KAAK,YAAY,WAAW;AAAA,EACtH;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,OAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,aAAa;AAAA,EACpF;AAAA;AAAA,EAEA,oCAAoC;AAClC,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,QAAI;AACJ,QAAI,CAAC,QAAQ;AACX,sBAAgB,GAAG,KAAK,OAAO,OAAO,UAAQ,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,IAC1E,OAAO;AACL,sBAAgB,iBAAiB,OAAO,YAAY,CAAC;AAAA,IACvD;AACA,kBAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC7C,iBAAW,QAAQ,OAAO;AACxB,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,aAAa,SAAS;AAAA,EAC7B;AACF;AAEA,SAAS,2BAA2B;AAClC,SAAO,CAAC,OAAO,YAAY,IAAI,eAAe,OAAO,OAAO;AAC9D;AAEA,IAAM,mBAAmB,IAAI,eAAe,oBAAoB;AAAA,EAC9D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AA0GD,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,WAAW,SAAS;AAGlB,WAAO,QAAQ,aAAa,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,SAAS;AACjB,WAAO,YAAY,OAAO,KAAK,iBAAiB,OAAO,EAAE,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAElB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,gBAAgB,UAAU,OAAO,CAAC;AACvD,QAAI,cAAc;AAEhB,UAAI,iBAAiB,YAAY,MAAM,IAAI;AACzC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,YAAY,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,QAAI,gBAAgB,iBAAiB,OAAO;AAC5C,QAAI,QAAQ,aAAa,iBAAiB,GAAG;AAC3C,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,YAAY,aAAa,UAAU;AAIlD,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,CAAC,yBAAyB,OAAO,GAAG;AACrF,aAAO;AAAA,IACT;AACA,QAAI,aAAa,SAAS;AAGxB,UAAI,CAAC,QAAQ,aAAa,UAAU,GAAG;AACrC,eAAO;AAAA,MACT;AAGA,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,SAAS;AAKxB,UAAI,kBAAkB,IAAI;AACxB,eAAO;AAAA,MACT;AAGA,UAAI,kBAAkB,MAAM;AAC1B,eAAO;AAAA,MACT;AAIA,aAAO,KAAK,UAAU,WAAW,QAAQ,aAAa,UAAU;AAAA,IAClE;AACA,WAAO,QAAQ,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,SAAS,QAAQ;AAG3B,WAAO,uBAAuB,OAAO,KAAK,CAAC,KAAK,WAAW,OAAO,MAAM,QAAQ,oBAAoB,KAAK,UAAU,OAAO;AAAA,EAC5H;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,SAAS,gBAAgBC,SAAQ;AAC/B,MAAI;AACF,WAAOA,QAAO;AAAA,EAChB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,SAAS;AAG5B,SAAO,CAAC,EAAE,QAAQ,eAAe,QAAQ,gBAAgB,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,EAAE;AACpI;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,SAAO,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY,aAAa;AAChG;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,eAAe,OAAO,KAAK,QAAQ,QAAQ;AACpD;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,gBAAgB,OAAO,KAAK,QAAQ,aAAa,MAAM;AAChE;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,QAAQ,aAAa,UAAU,KAAK,QAAQ,aAAa,QAAW;AACvE,WAAO;AAAA,EACT;AACA,MAAI,WAAW,QAAQ,aAAa,UAAU;AAC9C,SAAO,CAAC,EAAE,YAAY,CAAC,MAAM,SAAS,UAAU,EAAE,CAAC;AACrD;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,SAAS,QAAQ,aAAa,UAAU,KAAK,IAAI,EAAE;AACpE,SAAO,MAAM,QAAQ,IAAI,KAAK;AAChC;AAEA,SAAS,yBAAyB,SAAS;AACzC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,MAAI,YAAY,aAAa,WAAW,QAAQ;AAChD,SAAO,cAAc,UAAU,cAAc,cAAc,aAAa,YAAY,aAAa;AACnG;AAKA,SAAS,uBAAuB,SAAS;AAEvC,MAAI,cAAc,OAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,oBAAoB,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ,aAAa,iBAAiB,KAAK,iBAAiB,OAAO;AACzI;AAEA,SAAS,UAAU,MAAM;AAEvB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe;AACjE;AASA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA;AAAA,EAEf,sBAAsB,MAAM,KAAK,yBAAyB;AAAA,EAC1D,oBAAoB,MAAM,KAAK,0BAA0B;AAAA;AAAA,EAEzD,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,OAAO,KAAK,YAAY;AACnD,WAAK,sBAAsB,OAAO,KAAK,UAAU;AAAA,IACnD;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,YAAY,UAAU,UAAU,SAAS,WAAW,eAAe,OACnE,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,CAAC,cAAc;AACjB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa;AACf,kBAAY,oBAAoB,SAAS,KAAK,mBAAmB;AACjE,kBAAY,OAAO;AAAA,IACrB;AACA,QAAI,WAAW;AACb,gBAAU,oBAAoB,SAAS,KAAK,iBAAiB;AAC7D,gBAAU,OAAO;AAAA,IACnB;AACA,SAAK,eAAe,KAAK,aAAa;AACtC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AAEd,QAAI,KAAK,cAAc;AACrB,aAAO;AAAA,IACT;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,KAAK,cAAc;AACvC,aAAK,aAAa,iBAAiB,SAAS,KAAK,mBAAmB;AAAA,MACtE;AACA,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,KAAK,cAAc;AACrC,aAAK,WAAW,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,MAClE;AAAA,IACF,CAAC;AACD,QAAI,KAAK,SAAS,YAAY;AAC5B,WAAK,SAAS,WAAW,aAAa,KAAK,cAAc,KAAK,QAAQ;AACtE,WAAK,SAAS,WAAW,aAAa,KAAK,YAAY,KAAK,SAAS,WAAW;AAChF,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,SAAS;AACpC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,SAAS;AAC1C,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,0BAA0B,OAAO,CAAC,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kCAAkC,SAAS;AACzC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,yBAAyB,OAAO,CAAC,CAAC;AAAA,IAC7E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,OAAO;AAExB,UAAM,UAAU,KAAK,SAAS,iBAAiB,qBAAqB,KAAK,qBAA0B,KAAK,iBAAsB,KAAK,GAAG;AACtI,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAEvC,YAAI,QAAQ,CAAC,EAAE,aAAa,aAAa,KAAK,EAAE,GAAG;AACjD,kBAAQ,KAAK,gDAAgD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QACvL,WAAW,QAAQ,CAAC,EAAE,aAAa,oBAAoB,KAAK,EAAE,GAAG;AAC/D,kBAAQ,KAAK,uDAAuD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QAC9L;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,SAAS;AACpB,aAAO,QAAQ,SAAS,QAAQ,CAAC,IAAI,KAAK,yBAAyB,KAAK,QAAQ;AAAA,IAClF;AACA,WAAO,QAAQ,SAAS,QAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,wBAAwB,KAAK,QAAQ;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,SAAS;AAE3B,UAAM,oBAAoB,KAAK,SAAS,cAAc,wCAA6C;AACnG,QAAI,mBAAmB;AAErB,WAAK,OAAO,cAAc,eAAe,cAAc,kBAAkB,aAAa,mBAAmB,GAAG;AAC1G,gBAAQ,KAAK,2IAAqJ,iBAAiB;AAAA,MACrL;AAGA,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACpG,gBAAQ,KAAK,0DAA0D,iBAAiB;AAAA,MAC1F;AACA,UAAI,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACjD,cAAM,iBAAiB,KAAK,yBAAyB,iBAAiB;AACtE,wBAAgB,MAAM,OAAO;AAC7B,eAAO,CAAC,CAAC;AAAA,MACX;AACA,wBAAkB,MAAM,OAAO;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,SAAS;AACjC,UAAM,oBAAoB,KAAK,mBAAmB,OAAO;AACzD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS;AAChC,UAAM,oBAAoB,KAAK,mBAAmB,KAAK;AACvD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,yBAAyB,MAAM;AAC7B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,yBAAyB,SAAS,CAAC,CAAC,IAAI;AAC1H,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,MAAM;AAC5B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,wBAAwB,SAAS,CAAC,CAAC,IAAI;AACzH,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AACjD,SAAK,sBAAsB,KAAK,UAAU,MAAM;AAChD,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,UAAU,IAAI,uBAAuB;AAC5C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,WAAW,QAAQ;AAGvC,gBAAY,OAAO,aAAa,YAAY,GAAG,IAAI,OAAO,gBAAgB,UAAU;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACrB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,SAAS,KAAK,YAAY;AACrD,WAAK,sBAAsB,SAAS,KAAK,UAAU;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,IAAI;AAEnB,QAAI,KAAK,WAAW;AAClB,sBAAgB,IAAI;AAAA,QAClB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,EAAE;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,uBAAuB,OAAO;AAC5C,WAAO,IAAI,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,sBAAsB,KAAK,SAAS;AAAA,EACjH;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,UAAU;AAAA,EAC/B,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C;AAAA;AAAA,EAEA,4BAA4B;AAAA;AAAA,EAE5B,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW;AAAA,EACpC;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,SAAS,WAAW;AACtB,WAAK,YAAY,KAAK,kBAAkB,OAAO,KAAK,YAAY,eAAe,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,QAAQ;AAGxB,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,MAAM;AACrC,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,cAAc;AAC9B,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,YAAY,GAAG;AACnD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,oBAAoB,QAAQ,aAAa;AAC/C,QAAI,qBAAqB,CAAC,kBAAkB,eAAe,KAAK,eAAe,KAAK,WAAW,YAAY,GAAG;AAC5G,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,kCAAkC;AACnE,SAAK,WAAW,6BAA6B;AAAA,EAC/C;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,gBAAgB,WAAW,gBAAgB;AAAA,MACxD,aAAa,CAAC,GAAG,2BAA2B,eAAe,gBAAgB;AAAA,IAC7E;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,wBAAN,cAAoC,UAAU;AAAA,EAC5C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,kBAAkB,SAAS,IAAI;AAAA,IACtC,OAAO;AACL,WAAK,kBAAkB,WAAW,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,UAAU,SAAS,WAAW,mBAAmB,gBAAgB,QAAQ,UAAU;AACvG,UAAM,UAAU,UAAU,SAAS,WAAW,OAAO,OAAO,QAAQ;AACpE,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,SAAS,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,kBAAkB,WAAW,IAAI;AACtC,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe,aAAa,IAAI;AACrC,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,eAAe,WAAW,IAAI;AACnC,SAAK,cAAc,KAAK;AAAA,EAC1B;AACF;AAMA,IAAM,sCAAN,MAA0C;AAAA;AAAA,EAExC,YAAY;AAAA;AAAA,EAEZ,aAAa,WAAW;AAEtB,QAAI,KAAK,WAAW;AAClB,gBAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AAAA,IACvE;AACA,SAAK,YAAY,OAAK,KAAK,WAAW,WAAW,CAAC;AAClD,cAAU,QAAQ,kBAAkB,MAAM;AACxC,gBAAU,UAAU,iBAAiB,SAAS,KAAK,WAAW,IAAI;AAAA,IACpE,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,WAAW;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,cAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AACrE,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,WAAW,OAAO;AAC3B,UAAM,SAAS,MAAM;AACrB,UAAM,gBAAgB,UAAU;AAGhC,QAAI,UAAU,CAAC,cAAc,SAAS,MAAM,KAAK,CAAC,OAAO,UAAU,sBAAsB,GAAG;AAI1F,iBAAW,MAAM;AAEf,YAAI,UAAU,WAAW,CAAC,cAAc,SAAS,UAAU,UAAU,aAAa,GAAG;AACnF,oBAAU,0BAA0B;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAGhF,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA,EAGrB,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,WAAW;AAElB,SAAK,kBAAkB,KAAK,gBAAgB,OAAO,QAAM,OAAO,SAAS;AACzE,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,QAAQ;AAChB,YAAM,MAAM,SAAS,CAAC,EAAE,SAAS;AAAA,IACnC;AACA,UAAM,KAAK,SAAS;AACpB,cAAU,QAAQ;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,cAAU,SAAS;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,IAAI,MAAM,QAAQ,SAAS;AACjC,QAAI,MAAM,IAAI;AACZ,YAAM,OAAO,GAAG,CAAC;AACjB,UAAI,MAAM,QAAQ;AAChB,cAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,iBAAiB,iBAAiB,IAAI,oCAAoC;AAAA,EACjF;AAAA,EACA,OAAO,SAAS,SAAS;AAAA,IACvB,OAAO;AAAA,EACT,GAAG;AACD,QAAI;AACJ,QAAI,OAAO,WAAW,WAAW;AAC/B,qBAAe;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,WAAO,IAAI,sBAAsB,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,mBAAmB,KAAK,gBAAgB,cAAc,KAAK,SAAS;AAAA,EAClK;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,8BAA6B;AAAA,IACtC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,SAAS,gCAAgC,OAAO;AAM9C,SAAO,MAAM,YAAY,KAAK,MAAM,WAAW;AACjD;AAEA,SAAS,iCAAiC,OAAO;AAC/C,QAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,CAAC,KAAK,MAAM,kBAAkB,MAAM,eAAe,CAAC;AAKjG,SAAO,CAAC,CAAC,SAAS,MAAM,eAAe,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY;AAC7I;AAMA,IAAM,kCAAkC,IAAI,eAAe,qCAAqC;AAiBhG,IAAM,0CAA0C;AAAA,EAC9C,YAAY,CAAC,KAAK,SAAS,UAAU,MAAM,KAAK;AAClD;AAQA,IAAM,kBAAkB;AAKxB,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,SAAS;AACX;AAeA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAAA;AAAA,EAEpB,YAAY,IAAI,gBAAgB,IAAI;AAAA;AAAA,EAEpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,aAAa,WAAS;AAGpB,QAAI,KAAK,UAAU,YAAY,KAAK,aAAW,YAAY,MAAM,OAAO,GAAG;AACzE;AAAA,IACF;AACA,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,WAAS;AAItB,QAAI,KAAK,IAAI,IAAI,KAAK,eAAe,iBAAiB;AACpD;AAAA,IACF;AAGA,SAAK,UAAU,KAAK,gCAAgC,KAAK,IAAI,aAAa,OAAO;AACjF,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,WAAS;AAGvB,QAAI,iCAAiC,KAAK,GAAG;AAC3C,WAAK,UAAU,KAAK,UAAU;AAC9B;AAAA,IACF;AAGA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAMC,YAAW,OAAO,QAAQ;AAChC,UAAM,UAAU,OAAO,iCAAiC;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,kCACX,0CACA;AAGL,SAAK,mBAAmB,KAAK,UAAU,KAAK,KAAK,CAAC,CAAC;AACnD,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,qBAAqB,CAAC;AAGxE,QAAI,KAAK,UAAU,WAAW;AAC5B,YAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,WAAK,oBAAoB,OAAO,kBAAkB,MAAM;AACtD,eAAO,CAAC,sBAAsB,UAAUA,WAAU,WAAW,KAAK,YAAY,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,aAAa,KAAK,cAAc,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,cAAc,KAAK,eAAe,4BAA4B,CAAC;AAAA,MACjU,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,SAAS;AACxB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,+BAA+B,IAAI,eAAe,wBAAwB;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,uCAAuC;AAC9C,SAAO;AACT;AAEA,IAAM,iCAAiC,IAAI,eAAe,gCAAgC;AAC1F,IAAI,YAAY;AAChB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO,MAAM;AAAA,EACvB,kBAAkB,OAAO,gCAAgC;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,eAAe,OAAO,8BAA8B;AAAA,MACxD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,gBAAgB,KAAK,mBAAmB;AAAA,EAC9D;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,UAAM,iBAAiB,KAAK;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,UAAU;AACpD,iBAAW,KAAK,CAAC;AAAA,IACnB,OAAO;AACL,OAAC,YAAY,QAAQ,IAAI;AAAA,IAC3B;AACA,SAAK,MAAM;AACX,iBAAa,KAAK,gBAAgB;AAClC,QAAI,CAAC,YAAY;AACf,mBAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAAA,IACzF;AACA,QAAI,YAAY,QAAQ,gBAAgB;AACtC,iBAAW,eAAe;AAAA,IAC5B;AAEA,SAAK,aAAa,aAAa,aAAa,UAAU;AACtD,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,yBAAyB,KAAK,aAAa,EAAE;AAAA,IACpD;AAMA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,IAAI,QAAQ,aAAW,KAAK,kBAAkB,OAAO;AAAA,MAC9E;AACA,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB,WAAW,MAAM;AACvC,aAAK,aAAa,cAAc;AAChC,YAAI,OAAO,aAAa,UAAU;AAChC,eAAK,mBAAmB,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ;AAAA,QACjE;AAGA,aAAK,kBAAkB;AACvB,aAAK,kBAAkB,KAAK,kBAAkB;AAAA,MAChD,GAAG,GAAG;AACN,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,gBAAgB;AAClC,SAAK,cAAc,OAAO;AAC1B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,qBAAqB;AACnB,UAAM,eAAe;AACrB,UAAM,mBAAmB,KAAK,UAAU,uBAAuB,YAAY;AAC3E,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AAEjD,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,WAAO,UAAU,IAAI,YAAY;AACjC,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO,aAAa,aAAa,QAAQ;AACzC,WAAO,KAAK,sBAAsB,WAAW;AAC7C,SAAK,UAAU,KAAK,YAAY,MAAM;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,IAAI;AAO3B,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,aAAa;AAAA,EACrC,mBAAmB,OAAO,eAAe;AAAA,EACzC,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,UAAU,SAAS,UAAU,cAAc,QAAQ;AACtE,QAAI,KAAK,gBAAgB,OAAO;AAC9B,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,YAAY;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,KAAK,eAAe;AAC9B,WAAK,gBAAgB,KAAK,QAAQ,kBAAkB,MAAM;AACxD,eAAO,KAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,MAAM;AAErE,gBAAM,cAAc,KAAK,YAAY,cAAc;AAGnD,cAAI,gBAAgB,KAAK,wBAAwB;AAC/C,iBAAK,eAAe,SAAS,aAAa,KAAK,aAAa,KAAK,QAAQ;AACzE,iBAAK,yBAAyB;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AAAA;AAAA,EAEd;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,eAAe,YAAY;AAAA,MAC3C,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,IACjD;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,4BAA2B;AAMpC,EAAAA,2BAA0BA,2BAA0B,WAAW,IAAI,CAAC,IAAI;AAKxE,EAAAA,2BAA0BA,2BAA0B,UAAU,IAAI,CAAC,IAAI;AACzE,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAEhE,IAAM,gCAAgC,IAAI,eAAe,mCAAmC;AAK5F,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,yBAAyB,OAAO,qBAAqB;AAAA;AAAA,EAErD,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAAA;AAAA,EAE9B,eAAe,oBAAI,IAAI;AAAA;AAAA,EAEvB,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,8BAA8B,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAG3B,SAAK,iBAAiB;AACtB,SAAK,wBAAwB,WAAW,MAAM,KAAK,iBAAiB,KAAK;AAAA,EAC3E;AAAA;AAAA,EAEA,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,6BAA6B,IAAI,QAAQ;AAAA,EACzC,cAAc;AACZ,UAAM,UAAU,OAAO,+BAA+B;AAAA,MACpD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,iBAAiB,SAAS,iBAAiB,0BAA0B;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC,WAAS;AACvC,UAAM,SAAS,gBAAgB,KAAK;AAEpC,aAAS,UAAU,QAAQ,SAAS,UAAU,QAAQ,eAAe;AACnE,UAAI,MAAM,SAAS,SAAS;AAC1B,aAAK,SAAS,OAAO,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ,OAAO,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,gBAAgB,OAAO;AACtC,UAAM,gBAAgB,cAAc,OAAO;AAE3C,QAAI,CAAC,KAAK,UAAU,aAAa,cAAc,aAAa,GAAG;AAE7D,aAAO,GAAG;AAAA,IACZ;AAIA,UAAM,WAAW,eAAe,aAAa,KAAK,KAAK,aAAa;AACpE,UAAM,aAAa,KAAK,aAAa,IAAI,aAAa;AAEtD,QAAI,YAAY;AACd,UAAI,eAAe;AAIjB,mBAAW,gBAAgB;AAAA,MAC7B;AACA,aAAO,WAAW;AAAA,IACpB;AAEA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,IAAI,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,SAAK,aAAa,IAAI,eAAe,IAAI;AACzC,SAAK,yBAAyB,IAAI;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS;AACtB,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,cAAc,KAAK,aAAa,IAAI,aAAa;AACvD,QAAI,aAAa;AACf,kBAAY,QAAQ,SAAS;AAC7B,WAAK,YAAY,aAAa;AAC9B,WAAK,aAAa,OAAO,aAAa;AACtC,WAAK,uBAAuB,WAAW;AAAA,IACzC;AAAA,EACF;AAAA,EACA,SAAS,SAAS,QAAQ,SAAS;AACjC,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,iBAAiB,KAAK,aAAa,EAAE;AAI3C,QAAI,kBAAkB,gBAAgB;AACpC,WAAK,wBAAwB,aAAa,EAAE,QAAQ,CAAC,CAAC,gBAAgB,IAAI,MAAM,KAAK,eAAe,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IACnI,OAAO;AACL,WAAK,WAAW,MAAM;AAEtB,UAAI,OAAO,cAAc,UAAU,YAAY;AAC7C,sBAAc,MAAM,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,gBAAgB,kBAAkB;AAChC,QAAI,KAAK,SAAS;AAGhB,UAAI,KAAK,6BAA6B;AACpC,eAAO,KAAK,2BAA2B,gBAAgB,IAAI,UAAU;AAAA,MACvE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAUA,QAAI,KAAK,kBAAkB,KAAK,kBAAkB;AAChD,aAAO,KAAK;AAAA,IACd;AAKA,QAAI,oBAAoB,KAAK,iCAAiC,gBAAgB,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,2BAA2B,kBAAkB;AAW3C,WAAO,KAAK,mBAAmB,0BAA0B,YAAY,CAAC,CAAC,kBAAkB,SAAS,KAAK,uBAAuB,iBAAiB;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,QAAQ;AAC3B,YAAQ,UAAU,OAAO,eAAe,CAAC,CAAC,MAAM;AAChD,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,wBAAwB,WAAW,UAAU;AACtE,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,uBAAuB,WAAW,SAAS;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,QAAQ,oBAAoB,OAAO;AAC5C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU;AACf,WAAK,8BAA8B,WAAW,WAAW;AAMzD,UAAI,KAAK,mBAAmB,0BAA0B,WAAW;AAC/D,qBAAa,KAAK,gBAAgB;AAClC,cAAM,KAAK,KAAK,8BAA8B,kBAAkB;AAChE,aAAK,mBAAmB,WAAW,MAAM,KAAK,UAAU,MAAM,EAAE;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO,SAAS;AAOvB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,UAAM,mBAAmB,gBAAgB,KAAK;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,iBAAiB,YAAY,kBAAkB;AAC9E;AAAA,IACF;AACA,SAAK,eAAe,SAAS,KAAK,gBAAgB,gBAAgB,GAAG,WAAW;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,SAAS;AAGtB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,QAAI,CAAC,eAAe,YAAY,iBAAiB,MAAM,yBAAyB,QAAQ,QAAQ,SAAS,MAAM,aAAa,GAAG;AAC7H;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,aAAa,IAAI;AAAA,EACpC;AAAA,EACA,YAAY,MAAM,QAAQ;AACxB,QAAI,KAAK,QAAQ,UAAU,QAAQ;AACjC,WAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,yBAAyB,aAAa;AACpC,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B;AAAA,IACF;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ,KAAK;AACjF,QAAI,CAAC,wBAAwB;AAC3B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAS,iBAAiB,SAAS,KAAK,+BAA+B,2BAA2B;AAClG,iBAAS,iBAAiB,QAAQ,KAAK,+BAA+B,2BAA2B;AAAA,MACnG,CAAC;AAAA,IACH;AACA,SAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAEzE,QAAI,EAAE,KAAK,2BAA2B,GAAG;AAGvC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMF,UAAS,KAAK,WAAW;AAC/B,QAAAA,QAAO,iBAAiB,SAAS,KAAK,oBAAoB;AAAA,MAC5D,CAAC;AAED,WAAK,uBAAuB,iBAAiB,KAAK,UAAU,KAAK,0BAA0B,CAAC,EAAE,UAAU,cAAY;AAClH,aAAK;AAAA,UAAW;AAAA,UAAU;AAAA;AAAA,QAA4B;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,WAAW,YAAY;AAC7B,QAAI,KAAK,4BAA4B,IAAI,QAAQ,GAAG;AAClD,YAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ;AAC5E,UAAI,yBAAyB,GAAG;AAC9B,aAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAAA,MAC3E,OAAO;AACL,iBAAS,oBAAoB,SAAS,KAAK,+BAA+B,2BAA2B;AACrG,iBAAS,oBAAoB,QAAQ,KAAK,+BAA+B,2BAA2B;AACpG,aAAK,4BAA4B,OAAO,QAAQ;AAAA,MAClD;AAAA,IACF;AAEA,QAAI,CAAE,EAAE,KAAK,wBAAwB;AACnC,YAAMA,UAAS,KAAK,WAAW;AAC/B,MAAAA,QAAO,oBAAoB,SAAS,KAAK,oBAAoB;AAE7D,WAAK,2BAA2B,KAAK;AAErC,mBAAa,KAAK,qBAAqB;AACvC,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS,QAAQ,aAAa;AAC3C,SAAK,YAAY,SAAS,MAAM;AAChC,SAAK,YAAY,aAAa,MAAM;AACpC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAC/B,UAAM,UAAU,CAAC;AACjB,SAAK,aAAa,QAAQ,CAAC,MAAM,mBAAmB;AAClD,UAAI,mBAAmB,WAAW,KAAK,iBAAiB,eAAe,SAAS,OAAO,GAAG;AACxF,gBAAQ,KAAK,CAAC,gBAAgB,IAAI,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iCAAiC,kBAAkB;AACjD,UAAM;AAAA,MACJ,mBAAmB;AAAA,MACnB;AAAA,IACF,IAAI,KAAK;AAIT,QAAI,uBAAuB,WAAW,CAAC,oBAAoB,qBAAqB,oBAAoB,iBAAiB,aAAa,WAAW,iBAAiB,aAAa,cAAc,iBAAiB,UAAU;AAClN,aAAO;AAAA,IACT;AACA,UAAM,SAAS,iBAAiB;AAChC,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,gBAAgB,GAAG;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,YAAY;AAAA,EACnC;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB,IAAI,aAAa;AAAA,EAClC,cAAc;AAAA,EAAC;AAAA,EACf,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,uBAAuB,KAAK,cAAc,QAAQ,SAAS,QAAQ,aAAa,KAAK,QAAQ,aAAa,wBAAwB,CAAC,EAAE,UAAU,YAAU;AAC5J,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,GAAG,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,IAClF,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUG,mBAAkB;AAC3B,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC3D,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC7D,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,IAAM,2BAA2B;AAEjC,IAAM,2BAA2B;AAEjC,IAAM,sCAAsC;AAY5C,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,OAAO,kBAAkB,EAAE,QAAQ,yBAAyB,EAAE,UAAU,MAAM;AAC3G,UAAI,KAAK,6BAA6B;AACpC,aAAK,8BAA8B;AACnC,aAAK,qCAAqC;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAIA,UAAM,cAAc,KAAK,UAAU,cAAc,KAAK;AACtD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,WAAW;AAC7B,SAAK,UAAU,KAAK,YAAY,WAAW;AAK3C,UAAM,iBAAiB,KAAK,UAAU,eAAe;AACrD,UAAM,gBAAgB,kBAAkB,eAAe,mBAAmB,eAAe,iBAAiB,WAAW,IAAI;AACzH,UAAM,iBAAiB,iBAAiB,cAAc,mBAAmB,IAAI,QAAQ,MAAM,EAAE;AAC7F,gBAAY,OAAO;AACnB,YAAQ,eAAe;AAAA;AAAA,MAErB,KAAK;AAAA;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,iBAAiB;AAAA;AAAA,MAE1B,KAAK;AAAA;AAAA,MAEL,KAAK;AACH,eAAO,iBAAiB;AAAA,IAC5B;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,SAAK,wBAAwB,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,uCAAuC;AACrC,QAAI,CAAC,KAAK,+BAA+B,KAAK,UAAU,aAAa,KAAK,UAAU,MAAM;AACxF,YAAM,cAAc,KAAK,UAAU,KAAK;AACxC,kBAAY,OAAO,qCAAqC,0BAA0B,wBAAwB;AAC1G,WAAK,8BAA8B;AACnC,YAAM,OAAO,KAAK,oBAAoB;AACtC,UAAI,SAAS,iBAAiB,gBAAgB;AAC5C,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E,WAAW,SAAS,iBAAiB,gBAAgB;AACnD,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,0BAAyB;AAAA,IAClC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc;AACZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,IACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,EACtD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,MACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,WAAW,CAAC;AAElB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,SAAS,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,MAAM,QAAQ;AAGZ,QAAI,KAAK,WAAW,MAAM;AACxB,gBAAU,KAAK;AAAA,IACjB;AACA,QAAI,CAAC,SAAS,eAAe,MAAM,GAAG;AACpC,eAAS,MAAM,IAAI;AAAA,IACrB;AACA,WAAO,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACn7FH,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,cAAc,CAAC,CAAC;AACpD,IAAM,MAAM,CAAC,KAAK,0BAA0B;AAC5C,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AAChC,IAAM,MAAM,CAAC,YAAY,GAAG;AAC5B,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,WAAW,YAAY,WAAW;AAAA,EAC/F;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ;AAAA,EAC3C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,MAAM,OAAO,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,2BAA2B,EAAE;AAC1C,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,UAAU,IAAI,QAAQ,QAAQ;AAOpC,IAAM,kBAAN,MAAsB;AAAA,EACpB,OAAO,iBAAiB;AAAA,EACxB,OAAO,qBAAqB;AAAA,EAC5B,OAAO,qBAAqB;AAAA,EAC5B,OAAO,cAAc;AACvB;AAMA,IAAM,qBAAN,MAAyB;AAAA,EACvB,OAAO,UAAU;AAAA,EACjB,OAAO,WAAW;AAAA,EAClB,OAAO,UAAU;AACnB;AAOA,IAAM,yBAAyB,IAAI,eAAe,qBAAqB;AAAA,EACrE,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AASD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AAGZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU;AAAA,IACpB,SAAS,CAAC,UAAU;AAAA,EACtB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,UAAU;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,qBAAN,MAAyB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb;AAAA,EACA,YAAY,iBAAiB,WAAW,kBAAkB,aAAa,eAAe;AACpF,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK,oBAAoB,KAAK;AAC7C,UAAM,UAAU,KAAK,WAAW,KAAK;AACrC,UAAM,UAAU,KAAK,YAAY,KAAK,UAAU,UAAU;AAC1D,UAAM,WAAW,SAAS,aAAa,SAAS,MAAM,KAAK;AAC3D,QAAI,aAAa,UAAU;AACzB,WAAK,aAAa;AAClB,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AACF;AAGA,IAAM,kBAAkB,IAAI,eAAe,mBAAmB;AAAA,EAC5D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,0BAA0B;AACjC,SAAO,OAAO,SAAS;AACzB;AACA,IAAM,kBAAkB;AAExB,IAAM,cAAN,MAAkB;AAAA;AAAA,EAEhB;AAAA,EACA,iBAAiB,IAAI,QAAQ;AAAA;AAAA,EAE7B,gBAAgB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,QAAQ,QAAQ,OAAO,SAAS,SAAS;AACvC,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACb,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACf,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACf,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,OAAO,aAAa;AAC5B,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM,QAAQ;AACvB,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,KAAK;AACtB,WAAO,KAAK,eAAe,GAAG,KAAK,KAAK,QAAQ,GAAG,IAAI,MAAM;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,OAAO;AACjB,QAAI,SAAS,QAAQ,KAAK,eAAe,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AACtE,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO,QAAQ;AACzB,WAAO,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,EAChJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO,QAAQ;AACzB,WAAO,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,MAAM,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,MAAM;AAAA,EAC5J;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,QAAQ;AACtB,QAAI,SAAS,QAAQ;AACnB,UAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,UAAI,cAAc,KAAK,QAAQ,MAAM;AACrC,UAAI,cAAc,aAAa;AAC7B,eAAO,CAAC,KAAK,YAAY,OAAO,MAAM;AAAA,MACxC;AACA,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,QAAQ;AACtB,QAAI,SAAS,QAAQ;AACnB,YAAM,aAAa,KAAK,QAAQ,KAAK;AACrC,YAAM,cAAc,KAAK,QAAQ,MAAM;AACvC,UAAI,cAAc,aAAa;AAC7B,eAAO,CAAC,KAAK,YAAY,OAAO,MAAM;AAAA,MACxC;AACA,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,MAAM,KAAK,KAAK;AACxB,QAAI,OAAO,KAAK,YAAY,MAAM,GAAG,IAAI,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,YAAY,MAAM,GAAG,IAAI,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAO9D,IAAM,iBAAiB;AAYvB,IAAM,aAAa;AAEnB,SAAS,MAAM,QAAQ,eAAe;AACpC,QAAM,cAAc,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAY,CAAC,IAAI,cAAc,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AAEA,IAAM,oBAAN,MAAM,2BAA0B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,mBAAmB;AAAA;AAAA,EAEnB,iBAAiB,OAAO,iBAAiB;AAAA,IACvC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AACZ,UAAM;AACN,UAAM,gBAAgB,OAAO,iBAAiB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,kBAAkB,QAAW;AAC/B,WAAK,iBAAiB;AAAA,IACxB;AACA,UAAM,UAAU,KAAK,cAAc;AAAA,EACrC;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,SAAS,MAAM;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,IAAI,OAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,EAC/D;AAAA,EACA,eAAe;AACb,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,KAAK;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,IAAI,OAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,EACnE;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,GAAG,OAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,EAClE;AAAA,EACA,YAAY,MAAM;AAChB,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,KAAK,QAAQ,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,oBAAoB;AAGlB,QAAI,OAAO,SAAS,eAAe,KAAK,QAAQ;AAC9C,YAAM,SAAS,IAAI,KAAK,OAAO,KAAK,MAAM;AAG1C,YAAM,YAAY,OAAO,cAAc,KAAK,OAAO,WAAW,YAAY;AAG1E,aAAO,aAAa,IAAI,IAAI;AAAA,IAC9B;AAEA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,KAAK,QAAQ,KAAK,wBAAwB,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,EAClG;AAAA,EACA,MAAM,MAAM;AACV,WAAO,IAAI,KAAK,KAAK,QAAQ,CAAC;AAAA,EAChC;AAAA,EACA,WAAW,MAAM,OAAO,MAAM;AAC5B,QAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,UAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,cAAM,MAAM,wBAAwB,KAAK,4CAA4C;AAAA,MACvF;AACA,UAAI,OAAO,GAAG;AACZ,cAAM,MAAM,iBAAiB,IAAI,mCAAmC;AAAA,MACtE;AAAA,IACF;AACA,QAAI,SAAS,KAAK,wBAAwB,MAAM,OAAO,IAAI;AAE3D,QAAI,OAAO,SAAS,KAAK,UAAU,OAAO,cAAc,eAAe,YAAY;AACjF,YAAM,MAAM,iBAAiB,IAAI,2BAA2B,KAAK,IAAI;AAAA,IACvE;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,oBAAI,KAAK;AAAA,EAClB;AAAA,EACA,MAAM,OAAO,aAAa;AAGxB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO,QAAQ,IAAI,KAAK,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,EAC/C;AAAA,EACA,OAAO,MAAM,eAAe;AAC1B,QAAI,CAAC,KAAK,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,gDAAgD;AAAA,IAC9D;AACA,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ,iCAC5C,gBAD4C;AAAA,MAE/C,UAAU;AAAA,IACZ,EAAC;AACD,WAAO,KAAK,QAAQ,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,iBAAiB,MAAM,OAAO;AAC5B,WAAO,KAAK,kBAAkB,MAAM,QAAQ,EAAE;AAAA,EAChD;AAAA,EACA,kBAAkB,MAAM,QAAQ;AAC9B,QAAI,UAAU,KAAK,wBAAwB,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAK/G,QAAI,KAAK,SAAS,OAAO,OAAO,KAAK,SAAS,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI;AAC7E,gBAAU,KAAK,wBAAwB,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,OAAO,GAAG,CAAC;AAAA,IACzF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,MAAM;AAC1B,WAAO,KAAK,wBAAwB,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,IAAI;AAAA,EACxG;AAAA,EACA,UAAU,MAAM;AACd,WAAO,CAAC,KAAK,eAAe,GAAG,KAAK,QAAQ,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,QAAQ,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,EAChH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AAGA,UAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,YAAI,OAAO,IAAI,KAAK,KAAK;AACzB,YAAI,KAAK,QAAQ,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO,MAAM,YAAY,KAAK;AAAA,EAChC;AAAA,EACA,eAAe,KAAK;AAClB,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,CAAC,MAAM,KAAK,QAAQ,CAAC;AAAA,EAC9B;AAAA,EACA,UAAU;AACR,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAAA,EACA,QAAQ,QAAQ,OAAO,SAAS,SAAS;AACvC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,QAAQ,OAAO,GAAG,EAAE,GAAG;AAC1B,cAAM,MAAM,kBAAkB,KAAK,0CAA0C;AAAA,MAC/E;AACA,UAAI,CAAC,QAAQ,SAAS,GAAG,EAAE,GAAG;AAC5B,cAAM,MAAM,oBAAoB,OAAO,4CAA4C;AAAA,MACrF;AACA,UAAI,CAAC,QAAQ,SAAS,GAAG,EAAE,GAAG;AAC5B,cAAM,MAAM,oBAAoB,OAAO,4CAA4C;AAAA,MACrF;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,MAAM,MAAM;AAC/B,UAAM,SAAS,OAAO,SAAS,SAAS,CAAC;AACzC,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,UAAU,WAAW,aAAa;AAChC,QAAI,OAAO,cAAc,UAAU;AACjC,aAAO,qBAAqB,OAAO,IAAI,KAAK,UAAU,QAAQ,CAAC,IAAI;AAAA,IACrE;AACA,UAAM,QAAQ,UAAU,KAAK;AAC7B,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,KAAK,iBAAiB,KAAK;AAGxC,QAAI,WAAW,MAAM;AACnB,YAAM,gBAAgB,MAAM,QAAQ,oBAAoB,EAAE,EAAE,KAAK;AACjE,UAAI,cAAc,SAAS,GAAG;AAC5B,iBAAS,KAAK,iBAAiB,aAAa;AAAA,MAC9C;AAAA,IACF;AACA,WAAO,UAAU,KAAK,QAAQ;AAAA,EAChC;AAAA,EACA,WAAW,MAAM,QAAQ;AACvB,WAAO,IAAI,KAAK,KAAK,QAAQ,IAAI,SAAS,GAAI;AAAA,EAChD;AAAA;AAAA,EAEA,wBAAwB,MAAM,OAAO,MAAM;AAGzC,UAAM,IAAI,oBAAI,KAAK;AACnB,MAAE,YAAY,MAAM,OAAO,IAAI;AAC/B,MAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,YAAQ,OAAO,GAAG,MAAM,EAAE;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,QAAQ,KAAK,MAAM;AAGjB,UAAM,IAAI,oBAAI,KAAK;AACnB,MAAE,eAAe,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,CAAC;AACpE,MAAE,YAAY,KAAK,SAAS,GAAG,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG,KAAK,gBAAgB,CAAC;AAC3F,WAAO,IAAI,OAAO,CAAC;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AAQtB,UAAM,SAAS,MAAM,YAAY,EAAE,MAAM,UAAU;AACnD,QAAI,QAAQ;AACV,UAAI,QAAQ,SAAS,OAAO,CAAC,CAAC;AAC9B,YAAM,UAAU,SAAS,OAAO,CAAC,CAAC;AAClC,UAAI,UAAU,OAAO,CAAC,KAAK,OAAO,SAAY,SAAS,OAAO,CAAC,CAAC;AAChE,YAAM,OAAO,OAAO,CAAC;AACrB,UAAI,UAAU,IAAI;AAChB,gBAAQ,SAAS,OAAO,IAAI;AAAA,MAC9B,WAAW,SAAS,MAAM;AACxB,iBAAS;AAAA,MACX;AACA,UAAI,QAAQ,OAAO,GAAG,EAAE,KAAK,QAAQ,SAAS,GAAG,EAAE,MAAM,WAAW,QAAQ,QAAQ,SAAS,GAAG,EAAE,IAAI;AACpG,eAAO,KAAK,QAAQ,KAAK,MAAM,GAAG,OAAO,SAAS,WAAW,CAAC;AAAA,MAChE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,SAAS,QAAQ,OAAO,KAAK,KAAK;AAChC,SAAO,CAAC,MAAM,KAAK,KAAK,SAAS,OAAO,SAAS;AACnD;AACA,IAAM,0BAA0B;AAAA,EAC9B,OAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,yBAAyB,CAAC;AAAA,EACxC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,yBAAyB,CAAC;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,yBAAyB,UAAU,yBAAyB;AACnE,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AAGA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,SAAS,QAAQ,KAAK;AAAA,EACzE;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,8BAA6B;AAAA,EACxC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,WAAW,QAAQ,KAAK;AAAA,EAC3E;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,IAC3B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC9D,QAAQ,CAAC,4jBAA8jB;AAAA,IACvkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,4jBAA8jB;AAAA,IACzkB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,IACrD,WAAW,CAAC,GAAG,UAAU;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,SAAS,SAAS,OAAO,SAAS,SAAS,OAAO;AAGhD,QAAM,QAAQ,KAAK,UAAU,KAAK,CAAC,EAAE,UAAU,CAAC;AAAA,IAC9C;AAAA,EACF,MAAM;AACJ,aAAS,SAAS,GAAG,MAAM,WAAW,KAAK;AAC3C,aAAS,SAAS,GAAG,MAAM,WAAW,KAAK;AAC3C,aAAS,SAAS,GAAG,MAAM,eAAe,KAAK;AAC/C,QAAI,WAAW,KAAK,WAAW,GAAG;AAChC,eAAS,SAAS,GAAG,MAAM,IAAI,MAAM,SAAS,IAAI;AAAA,IACpD,WAAW,SAAS,GAAG;AACrB,eAAS,SAAS,GAAG,MAAM,eAAe,IAAI;AAAA,IAChD;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAS,SAAS,WAAW,OAAO;AAC3C,UAAQ,cAAc,UAAU,OAAO,WAAW,KAAK;AACzD;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,OAAO;AAAA,IAClC,SAAS,CAAC,SAAS,eAAe;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,OAAO;AAAA,MAClC,SAAS,CAAC,SAAS,eAAe;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,cAAa;AACtB,EAAAA,aAAYA,aAAY,WAAW,IAAI,CAAC,IAAI;AAC5C,EAAAA,aAAYA,aAAY,SAAS,IAAI,CAAC,IAAI;AAC1C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,EAAAA,aAAYA,aAAY,QAAQ,IAAI,CAAC,IAAI;AAC3C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAIpC,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,QAAQ,YAAY;AAAA,EACpB,YAAY,WACZ,SACA,QACA,uCAAuC,OAAO;AAC5C,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,uCAAuC;AAAA,EAC9C;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,UAAU,cAAc,IAAI;AAAA,EACnC;AACF;AAGA,IAAM,iCAAiC,gCAAgC;AAAA,EACrE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,qBAAN,MAAyB;AAAA,EACvB,UAAU,oBAAI,IAAI;AAAA;AAAA,EAElB,WAAW,QAAQ,MAAM,SAAS,SAAS;AACzC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,kBAAkB;AACpB,YAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,UAAI,oBAAoB;AACtB,2BAAmB,IAAI,OAAO;AAAA,MAChC,OAAO;AACL,yBAAiB,IAAI,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAAA,MAClD;AAAA,IACF,OAAO;AACL,WAAK,QAAQ,IAAI,MAAM,oBAAI,IAAI,CAAC,CAAC,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,aAAO,kBAAkB,MAAM;AAC7B,iBAAS,iBAAiB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM,SAAS,SAAS;AACpC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,UAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AACA,uBAAmB,OAAO,OAAO;AACjC,QAAI,mBAAmB,SAAS,GAAG;AACjC,uBAAiB,OAAO,OAAO;AAAA,IACjC;AACA,QAAI,iBAAiB,SAAS,GAAG;AAC/B,WAAK,QAAQ,OAAO,IAAI;AACxB,eAAS,oBAAoB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,IAC/F;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAS;AAC/B,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,QAAQ;AACV,WAAK,QAAQ,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,YAAY;AAC3D,YAAI,YAAY,UAAU,QAAQ,SAAS,MAAM,GAAG;AAClD,mBAAS,QAAQ,aAAW,QAAQ,YAAY,KAAK,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAMA,IAAM,+BAA+B;AAAA,EACnC,eAAe;AAAA,EACf,cAAc;AAChB;AAKA,IAAM,2BAA2B;AAEjC,IAAM,+BAA+B,gCAAgC;AAAA,EACnE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,oBAAoB,CAAC,aAAa,YAAY;AAEpD,IAAM,kBAAkB,CAAC,WAAW,cAAc,YAAY,aAAa;AAC3E,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,2BAA2B,EAAE;AAAA,IACzC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC7D,QAAQ,CAAC,2jBAA2jB;AAAA,IACpkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,2BAA2B;AAAA,MAC7B;AAAA,MACA,QAAQ,CAAC,2jBAA2jB;AAAA,IACtkB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,iBAAiB,oBAAI,IAAI;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B;AAAA,EACA,OAAO,gBAAgB,IAAI,mBAAmB;AAAA,EAC9C,YAAY,SAAS,SAAS,qBAAqB,WAAW,UAAU;AACtE,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,QAAI,UAAU,WAAW;AACvB,WAAK,oBAAoB,cAAc,mBAAmB;AAAA,IAC5D;AACA,QAAI,UAAU;AACZ,eAAS,IAAI,sBAAsB,EAAE,KAAK,sBAAsB;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG,GAAG,SAAS,CAAC,GAAG;AAC9B,UAAM,gBAAgB,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,sBAAsB;AAChH,UAAM,kBAAkB,kCACnB,+BACA,OAAO;AAEZ,QAAI,OAAO,UAAU;AACnB,UAAI,cAAc,OAAO,cAAc,QAAQ;AAC/C,UAAI,cAAc,MAAM,cAAc,SAAS;AAAA,IACjD;AACA,UAAM,SAAS,OAAO,UAAU,yBAAyB,GAAG,GAAG,aAAa;AAC5E,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,gBAAgB,gBAAgB;AACtC,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,UAAU,IAAI,oBAAoB;AACzC,WAAO,MAAM,OAAO,GAAG,UAAU,MAAM;AACvC,WAAO,MAAM,MAAM,GAAG,UAAU,MAAM;AACtC,WAAO,MAAM,SAAS,GAAG,SAAS,CAAC;AACnC,WAAO,MAAM,QAAQ,GAAG,SAAS,CAAC;AAGlC,QAAI,OAAO,SAAS,MAAM;AACxB,aAAO,MAAM,kBAAkB,OAAO;AAAA,IACxC;AACA,WAAO,MAAM,qBAAqB,GAAG,aAAa;AAClD,SAAK,kBAAkB,YAAY,MAAM;AAKzC,UAAM,iBAAiB,OAAO,iBAAiB,MAAM;AACrD,UAAM,yBAAyB,eAAe;AAC9C,UAAM,yBAAyB,eAAe;AAM9C,UAAM,sCAAsC,2BAA2B;AAAA;AAAA,IAGvE,2BAA2B,QAAQ,2BAA2B;AAAA,IAE9D,cAAc,UAAU,KAAK,cAAc,WAAW;AAEtD,UAAM,YAAY,IAAI,UAAU,MAAM,QAAQ,QAAQ,mCAAmC;AAKzF,WAAO,MAAM,YAAY;AACzB,cAAU,QAAQ,YAAY;AAC9B,QAAI,CAAC,OAAO,YAAY;AACtB,WAAK,6BAA6B;AAAA,IACpC;AACA,QAAI,iBAAiB;AAGrB,QAAI,CAAC,wCAAwC,iBAAiB,gBAAgB,eAAe;AAC3F,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,kBAAkB,MAAM;AAE5B,cAAI,gBAAgB;AAClB,2BAAe,gBAAgB;AAAA,UACjC;AACA,uBAAa,aAAa;AAC1B,eAAK,wBAAwB,SAAS;AAAA,QACxC;AACA,cAAM,qBAAqB,MAAM,KAAK,eAAe,SAAS;AAQ9D,cAAM,gBAAgB,WAAW,oBAAoB,gBAAgB,GAAG;AACxE,eAAO,iBAAiB,iBAAiB,eAAe;AAIxD,eAAO,iBAAiB,oBAAoB,kBAAkB;AAC9D,yBAAiB;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,eAAe,IAAI,WAAW,cAAc;AAGjD,QAAI,uCAAuC,CAAC,eAAe;AACzD,WAAK,wBAAwB,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AAEvB,QAAI,UAAU,UAAU,YAAY,cAAc,UAAU,UAAU,YAAY,QAAQ;AACxF;AAAA,IACF;AACA,UAAM,WAAW,UAAU;AAC3B,UAAM,kBAAkB,kCACnB,+BACA,UAAU,OAAO;AAItB,aAAS,MAAM,qBAAqB,GAAG,gBAAgB,YAAY;AACnE,aAAS,MAAM,UAAU;AACzB,cAAU,QAAQ,YAAY;AAG9B,QAAI,UAAU,wCAAwC,CAAC,gBAAgB,cAAc;AACnF,WAAK,wBAAwB,SAAS;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,kBAAkB,EAAE,QAAQ,YAAU,OAAO,QAAQ,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AACzC,UAAI,CAAC,OAAO,OAAO,YAAY;AAC7B,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB,qBAAqB;AACtC,UAAM,UAAU,cAAc,mBAAmB;AACjD,QAAI,CAAC,KAAK,UAAU,aAAa,CAAC,WAAW,YAAY,KAAK,iBAAiB;AAC7E;AAAA,IACF;AAEA,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AAGvB,sBAAkB,QAAQ,UAAQ;AAChC,sBAAe,cAAc,WAAW,KAAK,SAAS,MAAM,SAAS,IAAI;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,QAAI,MAAM,SAAS,aAAa;AAC9B,WAAK,aAAa,KAAK;AAAA,IACzB,WAAW,MAAM,SAAS,cAAc;AACtC,WAAK,cAAc,KAAK;AAAA,IAC1B,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAIA,QAAI,CAAC,KAAK,4BAA4B;AAMpC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,wBAAgB,QAAQ,UAAQ;AAC9B,eAAK,gBAAgB,iBAAiB,MAAM,MAAM,4BAA4B;AAAA,QAChF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,6BAA6B;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAW;AACjC,QAAI,UAAU,UAAU,YAAY,WAAW;AAC7C,WAAK,wBAAwB,SAAS;AAAA,IACxC,WAAW,UAAU,UAAU,YAAY,YAAY;AACrD,WAAK,eAAe,SAAS;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,WAAW;AACjC,UAAM,8BAA8B,cAAc,KAAK;AACvD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,UAAU;AACd,cAAU,QAAQ,YAAY;AAK9B,QAAI,CAAC,eAAe,CAAC,+BAA+B,CAAC,KAAK,iBAAiB;AACzE,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,UAAM,iBAAiB,KAAK,eAAe,IAAI,SAAS,KAAK;AAC7D,SAAK,eAAe,OAAO,SAAS;AAEpC,QAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AAGA,QAAI,cAAc,KAAK,4BAA4B;AACjD,WAAK,6BAA6B;AAAA,IACpC;AACA,cAAU,QAAQ,YAAY;AAC9B,QAAI,mBAAmB,MAAM;AAC3B,gBAAU,QAAQ,oBAAoB,iBAAiB,eAAe,eAAe;AACrF,gBAAU,QAAQ,oBAAoB,oBAAoB,eAAe,kBAAkB;AAC3F,UAAI,eAAe,kBAAkB,MAAM;AACzC,qBAAa,eAAe,aAAa;AAAA,MAC3C;AAAA,IACF;AACA,cAAU,QAAQ,OAAO;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa,OAAO;AAGlB,UAAM,kBAAkB,gCAAgC,KAAK;AAC7D,UAAM,mBAAmB,KAAK,wBAAwB,KAAK,IAAI,IAAI,KAAK,uBAAuB;AAC/F,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,mBAAmB,CAAC,kBAAkB;AACzE,WAAK,iBAAiB;AACtB,WAAK,aAAa,MAAM,SAAS,MAAM,SAAS,KAAK,QAAQ,YAAY;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,iCAAiC,KAAK,GAAG;AAI5E,WAAK,uBAAuB,KAAK,IAAI;AACrC,WAAK,iBAAiB;AAGtB,YAAM,UAAU,MAAM;AAGtB,UAAI,SAAS;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,eAAK,aAAa,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,YAAY;AAAA,QACrF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AAGzC,YAAM,YAAY,OAAO,UAAU,YAAY,WAAW,OAAO,OAAO,wBAAwB,OAAO,UAAU,YAAY;AAC7H,UAAI,CAAC,OAAO,OAAO,cAAc,WAAW;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,MAAM,KAAK,KAAK,eAAe,KAAK,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,wBAAkB,QAAQ,UAAQ,gBAAe,cAAc,cAAc,MAAM,SAAS,IAAI,CAAC;AACjG,UAAI,KAAK,4BAA4B;AACnC,wBAAgB,QAAQ,UAAQ,QAAQ,oBAAoB,MAAM,MAAM,4BAA4B,CAAC;AACrG,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,yBAAyB,GAAG,GAAG,MAAM;AAC5C,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AACxE,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC;AACxE,SAAO,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAChD;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAChF,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,OAAO;AACT,WAAK,wBAAwB;AAAA,IAC/B;AACA,SAAK,YAAY;AACjB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK,YAAY;AAAA,EAC3C;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA,EACjB,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,WAAW,OAAO,QAAQ;AAChC,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,QAAQ;AAGhC,SAAK,iBAAiB,iBAAiB,CAAC;AACxC,SAAK,kBAAkB,IAAI,eAAe,MAAM,QAAQ,KAAK,aAAa,UAAU,QAAQ;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AACtB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,qBAAqB;AAAA,EAC5C;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,gBAAgB,WAAW;AAAA,EAClC;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,gBAAgB,wBAAwB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,WAAW,iDACN,KAAK,eAAe,YACnB,KAAK,mBAAmB,mBAAmB;AAAA,QAC7C,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,IAAI,CAAC,IACF,KAAK;AAAA,MAEV,sBAAsB,KAAK,eAAe;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK,YAAY,CAAC,CAAC,KAAK,eAAe;AAAA,EAChD;AAAA;AAAA,EAEA,+BAA+B;AAC7B,QAAI,CAAC,KAAK,YAAY,KAAK,gBAAgB;AACzC,WAAK,gBAAgB,mBAAmB,KAAK,OAAO;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,WAAW,IAAI,GAAG,QAAQ;AAC/B,QAAI,OAAO,cAAc,UAAU;AACjC,aAAO,KAAK,gBAAgB,aAAa,WAAW,GAAG,kCAClD,KAAK,eACL,OACJ;AAAA,IACH,OAAO;AACL,aAAO,KAAK,gBAAgB,aAAa,GAAG,GAAG,kCAC1C,KAAK,eACL,UACJ;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACzD,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,wBAAwB,IAAI,SAAS;AAAA,MACtD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,kBAAkB,OAAO;AAAA,MACpC,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,UAAU,CAAC,GAAG,qBAAqB,UAAU;AAAA,MAC7C,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,UAAU,CAAC,GAAG,qBAAqB,UAAU;AAAA,MAC7C,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,IAC5C;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,SAAS;AAAA,IACpC,SAAS,CAAC,WAAW,eAAe;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,SAAS;AAAA,MACpC,SAAS,CAAC,WAAW,eAAe;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAeH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,QAAQ;AAAA;AAAA,EAER,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA,EACb,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qCAAqC,IAAI,UAAU,eAAe,EAAE,+BAA+B,IAAI,UAAU,SAAS,EAAE,gCAAgC,IAAI,QAAQ,EAAE,+BAA+B,IAAI,eAAe,SAAS,EAAE,4BAA4B,IAAI,eAAe,MAAM,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB;AAAA,MACjX;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AAAA,IAAC;AAAA,IACxD,QAAQ,CAAC,6xGAA+xG;AAAA,IACxyG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6CAA6C;AAAA,QAC7C,uCAAuC;AAAA,QACvC,wCAAwC;AAAA,QACxC,uCAAuC;AAAA,QACvC,oCAAoC;AAAA,QACpC,mCAAmC;AAAA,MACrC;AAAA,MACA,QAAQ,CAAC,6xGAA+xG;AAAA,IAC1yG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,IAC5C,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,MAC5C,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,8BAA8B,IAAI,eAAe,6BAA6B;AA0BpF,IAAM,eAAe,IAAI,eAAe,aAAa;AAIrD,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA,EAEhB;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX,WAAW,OAAO,YAAY,EAAE,MAAM,qBAAqB;AAAA;AAAA,EAE3D;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,6BAA6B;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS,QAAQ,eAAe;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,SAAS,OAAO,OAAO,EAAE,iBAAiB,IAAI,SAAS,OAAO,IAAI,SAAS,SAAS,CAAC,EAAE,mBAAmB,IAAI,SAAS,OAAO,IAAI,QAAQ;AAAA,MACvK;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,gBAAgB,GAAG,0BAA0B,GAAG,IAAI,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC3G,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,QAAG,OAAO,CAAC;AACX,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,GAAG,CAAC;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,IAAI,QAAQ;AACtD,QAAG,WAAW,MAAM,IAAI,QAAQ;AAChC,QAAG,UAAU,CAAC;AACd,QAAG,mBAAmB,IAAI,IAAI,OAAO,GAAG;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,y9BAAy9B;AAAA,IACl+B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,0BAA0B;AAAA,MAC5B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA,MACV,QAAQ,CAAC,y9BAAy9B;AAAA,IACp+B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,2BAAN,MAA+B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,YACA,QACA,cAAc,OAAO;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AACF;AAIA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,WAAW,OAAO,UAAU;AAAA,EAC5B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,UAAU,OAAO,6BAA6B;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,QAAQ,OAAO,cAAc;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB;AAAA,EACvB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,uBAAuB;AAAA;AAAA,EAEvB,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,QAAQ;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,KAAK,OAAO,YAAY,EAAE,MAAM,aAAa;AAAA;AAAA,EAE7C,IAAI,WAAW;AACb,WAAO,KAAK,SAAS,KAAK,MAAM,YAAY,KAAK;AAAA,EACnD;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,uBAAuB,KAAK,QAAQ,cAAc,IAAI,CAAC,CAAC,KAAK,SAAS;AAAA,EACpF;AAAA;AAAA,EAEA,IAAI,+BAA+B;AACjC,WAAO,CAAC,EAAE,KAAK,WAAW,KAAK,QAAQ;AAAA,EACzC;AAAA;AAAA;AAAA,EAGA,oBAAoB,IAAI,aAAa;AAAA;AAAA,EAErC;AAAA;AAAA,EAEA,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,cAAc;AACZ,UAAM,cAAc,OAAO,sBAAsB;AACjD,gBAAY,KAAK,uBAAuB;AACxC,gBAAY,KAAK,qBAAqB;AACtC,SAAK,uBAAuB,CAAC,CAAC,KAAK,WAAW,SAAS,KAAK,QAAQ,aAAa;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AAEd,YAAQ,KAAK,OAAO,cAAc,eAAe,IAAI,KAAK;AAAA,EAC5D;AAAA;AAAA,EAEA,OAAO,YAAY,MAAM;AACvB,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AACrC,UAAI,WAAW;AACb,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,YAAY,MAAM;AACzB,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AACrC,UAAI,WAAW;AACb,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,SAAS,SAAS;AAGtB,UAAM,UAAU,KAAK,gBAAgB;AACrC,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,cAAQ,MAAM,OAAO;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,SAAK,MAAM,YAAY,SAAS,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AAClF,WAAK,sBAAsB;AAE3B,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,WAAW,CAAC,KAAK,YAAY;AACnD,WAAK,mBAAmB,aAAa;AACrC,WAAK,0BAA0B,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,qBAAqB;AAMnB,QAAI,KAAK,WAAW;AAClB,YAAM,YAAY,KAAK;AACvB,UAAI,cAAc,KAAK,sBAAsB;AAC3C,YAAI,KAAK,sBAAsB;AAC7B,eAAK,cAAc,KAAK;AAAA,QAC1B;AACA,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA;AAAA,EAEA,0BAA0B,cAAc,OAAO;AAC7C,SAAK,kBAAkB,KAAK,IAAI,yBAAyB,MAAM,WAAW,CAAC;AAAA,EAC7E;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,UAAU,GAAG,kBAAkB,eAAe;AAAA,IAClE,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,qCAAqC;AACnE,iBAAO,IAAI,sBAAsB;AAAA,QACnC,CAAC,EAAE,WAAW,SAAS,qCAAqC,QAAQ;AAClE,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,iBAAiB,IAAI,QAAQ,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC;AACtF,QAAG,YAAY,2BAA2B,IAAI,QAAQ,EAAE,2BAA2B,IAAI,QAAQ,EAAE,yBAAyB,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ;AAAA,MAC/K;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,kCAAkC,GAAG,YAAY,OAAO,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,SAAS,WAAW,eAAe,QAAQ,cAAc,WAAW,GAAG,kCAAkC,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,eAAe,QAAQ,cAAc,IAAI,GAAG,yBAAyB,uBAAuB,GAAG,oBAAoB,mBAAmB,CAAC;AAAA,IAC/a,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,uBAAuB,CAAC;AACjF,QAAG,aAAa,CAAC;AACjB,QAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,uBAAuB,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,QAAQ,CAAC;AACvI,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,+BAA+B,IAAI,EAAE;AAC5F,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI,EAAE;AACvD,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,gBAAgB,CAAC,EAAE,qBAAqB,IAAI,YAAY,IAAI,aAAa;AAAA,MACjH;AAAA,IACF;AAAA,IACA,cAAc,CAAC,mBAAmB,SAAS;AAAA,IAC3C,QAAQ,CAAC,2uHAA+uH;AAAA,IACxvH,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,mCAAmC;AAAA,QACnC,mCAAmC;AAAA,QACnC,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUR,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,mBAAmB,SAAS;AAAA,MACtC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,2uHAA+uH;AAAA,IAC1vH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,SAAS,8BAA8B,aAAa,SAAS,cAAc;AACzE,MAAI,aAAa,QAAQ;AACvB,QAAI,eAAe,QAAQ,QAAQ;AACnC,QAAI,SAAS,aAAa,QAAQ;AAClC,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,cAAc,GAAG,KAAK;AACxC,UAAI,aAAa,CAAC,EAAE,SAAS,aAAa,CAAC,EAAE,UAAU,OAAO,YAAY,GAAG;AAC3E;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AASA,SAAS,yBAAyB,cAAc,cAAc,uBAAuB,aAAa;AAChG,MAAI,eAAe,uBAAuB;AACxC,WAAO;AAAA,EACT;AACA,MAAI,eAAe,eAAe,wBAAwB,aAAa;AACrE,WAAO,KAAK,IAAI,GAAG,eAAe,cAAc,YAAY;AAAA,EAC9D;AACA,SAAO;AACT;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,yBAAyB,WAAW,WAAW;AAAA,IAC3F,SAAS,CAAC,WAAW,WAAW;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,iBAAiB,uBAAuB;AAAA,EACrE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,yBAAyB,WAAW,WAAW;AAAA,MAC3F,SAAS,CAAC,WAAW,WAAW;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AACX;AAMA,IAAM,0BAA0B,CAAC,SAAS,aAAa,cAAc,YAAY;AAEjF,IAAM,yBAAyB;AAE/B,IAAM,qBAAqB;AAE3B,IAAM,oBAAoB;AAE1B,IAAM,oBAAoB;AAS1B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,OAAO,QAAQ;AAAA,EAC3B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB,OAAO,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,SAAS,oBAAI,IAAI;AAAA,EACjB,cAAc;AACZ,UAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,SAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM;AACzD,aAAO,wBAAwB,IAAI,UAAQ,sBAAsB,UAAU,KAAK,WAAW,MAAM,KAAK,gBAAgB,oBAAoB,CAAC;AAAA,IAC7I,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,eAAW,QAAQ,OAAO;AACxB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,MAAM,QAAQ;AAE5B,SAAK,aAAa,wBAAwB,KAAK,sBAAsB,aAAa,EAAE;AAEpF,QAAI,OAAO,aAAa,CAAC,KAAK,aAAa,kBAAkB,GAAG;AAC9D,WAAK,aAAa,oBAAoB,OAAO,aAAa,EAAE;AAAA,IAC9D;AAEA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AACA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,MAAM,UAAU;AAC1B,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AAEnC,QAAI,QAAQ;AACV,aAAO,OAAO,iBAAiB;AAC/B,UAAI,CAAC,YAAY,CAAC,OAAO,gBAAgB;AACvC,eAAO,iBAAiB;AACxB,eAAO,SAAS,mBAAmB,IAAI;AAAA,MACzC;AAAA,IACF,WAAW,UAAU;AAGnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC,OAAO;AACL,WAAK,gBAAgB,iBAAiB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,WAAS;AACxB,UAAM,cAAc,gBAAgB,KAAK;AACzC,QAAI,uBAAuB,aAAa;AAEtC,YAAM,UAAU,YAAY,QAAQ,IAAI,sBAAsB,KAAK,KAAK,sBAAsB,aAAa,EAAE,IAAI;AACjH,UAAI,SAAS;AACX,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM;AAClB,QAAI,CAAC,KAAK,aAAa,KAAK,OAAO,IAAI,IAAI,GAAG;AAC5C;AAAA,IACF;AAEA,SAAK,cAAc,aAAa,GAAG,OAAO;AAC1C,UAAM,WAAW,KAAK,UAAU,cAAc,MAAM;AACpD,aAAS,UAAU,IAAI,cAAc,KAAK,aAAa,kBAAkB,CAAC;AAC1E,SAAK,OAAO,QAAQ;AACpB,UAAM,mBAAmB,KAAK,mBAAmB;AACjD,UAAM,gBAAgB,KAAK;AAC3B,UAAM,gBAAgB,mBAAmB,IAAI,eAAe,WAAW,iBAAiB,6BAA6B;AACrH,UAAM,eAAe,mBAAmB,IAAI,eAAe,WAAW,gBAAgB,6BAA6B;AACnH,UAAM,SAAS;AAAA,MACb,gBAAgB,oBAAoB,eAAe,YAAY,KAAK,aAAa,iBAAiB;AAAA,MAClG,cAAc;AAAA,QACZ,UAAU,KAAK,aAAa,iBAAiB;AAAA,QAC7C,sBAAsB,eAAe;AAAA,QACrC,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,WAAW,IAAI,eAAe,QAAQ,KAAK,SAAS,UAAU,KAAK,WAAW,KAAK,SAAS;AAClG,UAAM,iBAAiB,CAAC,OAAO;AAC/B,QAAI,gBAAgB;AAClB,eAAS,mBAAmB,IAAI;AAAA,IAClC;AACA,SAAK,OAAO,IAAI,MAAM;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,gBAAgB,sBAAsB;AAAA,EAC7C;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,SAAS,qBAAqB;AACrC,WAAK,OAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,wBAAN,MAAM,uBAAsB;AAAA;AAAA,EAE1B;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,OAAO,2BAA2B,EAAE,CAAC;AAAA,IAClD,WAAW,CAAC,GAAG,kBAAkB,yBAAyB;AAAA,IAC1D,UAAU;AAAA,IACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,6BAA6B,IAAI,kBAAkB,QAAQ;AAAA,MAC5E;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,imBAAimB;AAAA,IAC1mB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,qCAAqC;AAAA,MACvC;AAAA,MACA,QAAQ,CAAC,imBAAimB;AAAA,IAC5mB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": ["items", "window", "document", "FocusMonitorDetectionMode", "HighContrastMode", "RippleState"]}