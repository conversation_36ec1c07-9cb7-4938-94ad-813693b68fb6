#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from core.models import User, Project, Post, Company
from django.contrib.auth import get_user_model

def debug_posts():
    print("=== DEBUG POSTS ===")
    
    # Check users
    print(f"\nTotal Users: {User.objects.count()}")
    creators = User.objects.filter(role='creator')
    print(f"Creators: {creators.count()}")
    
    for creator in creators:
        print(f"\nCreator: {creator.username} ({creator.email})")
        assigned_projects = creator.assigned_projects.all()
        print(f"  Assigned Projects: {[p.name for p in assigned_projects]}")
        
        # Check posts created by this creator
        creator_posts = Post.objects.filter(creator=creator)
        print(f"  Total Posts by Creator: {creator_posts.count()}")
        
        # Check posts in assigned projects
        posts_in_assigned = Post.objects.filter(
            creator=creator,
            project__in=assigned_projects
        )
        print(f"  Posts in Assigned Projects: {posts_in_assigned.count()}")
        
        if posts_in_assigned.exists():
            for post in posts_in_assigned[:3]:
                print(f"    - {post.title} (Project: {post.project.name}, Status: {post.status})")
    
    # Check all posts
    print(f"\nTotal Posts: {Post.objects.count()}")
    print(f"Total Projects: {Project.objects.count()}")
    print(f"Total Companies: {Company.objects.count()}")
    
    # Check if there are posts without creators assigned to projects
    from django.db import models
    orphaned_posts = Post.objects.exclude(
        project__creators=models.F('creator')
    )
    print(f"Orphaned Posts (creator not assigned to project): {orphaned_posts.count()}")
    
    if orphaned_posts.exists():
        print("Sample orphaned posts:")
        for post in orphaned_posts[:5]:
            print(f"  - {post.title} by {post.creator.username} in {post.project.name}")
            print(f"    Project creators: {[c.username for c in post.project.creators.all()]}")

if __name__ == "__main__":
    debug_posts()
