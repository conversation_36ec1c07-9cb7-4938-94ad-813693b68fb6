from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone

# Create your models here.
class User(AbstractUser):
    ROLE_CHOICES = (
        ('super_admin', 'Super Admin'),
        ('company_admin', 'Company Admin'),
        ('creator', 'Creator'),
    )
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    company = models.ForeignKey('Company', null=True, blank=True, on_delete=models.SET_NULL, related_name='users')

    def __str__(self):
        return f"{self.username} ({self.role})"

class Company(models.Model):
    name = models.CharField(max_length=255)
    # Remove the confusing 'creator' field - creators are linked via User.company
    logo = models.ImageField(upload_to='company_logos/', null=True, blank=True)

    def __str__(self):
        return self.name

    @property
    def company_admins(self):
        """Get all company admins assigned to this company"""
        return self.users.filter(role='company_admin')

    @property
    def creators(self):
        """Get all creators assigned to this company"""
        return self.users.filter(role='creator')

class Project(models.Model):
    name = models.CharField(max_length=255)
    title = models.CharField(max_length=255, blank=True, null=True, help_text="Project title for display")
    description = models.TextField(blank=True, null=True, help_text="Project description")
    deadline = models.DateTimeField(null=True, blank=True, help_text="Project deadline")
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='projects')
    creators = models.ManyToManyField(User, related_name='assigned_projects', limit_choices_to={'role': 'creator'})
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_projects')
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.title or self.name} ({self.company.name})"



class Post(models.Model):
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='posts')
    creator = models.ForeignKey('User', on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    description = models.TextField()
    media = models.FileField(upload_to='posts/', null=True, blank=True)
    scheduled_date = models.DateTimeField(null=True, blank=True)
    scheduled_time = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=20, choices=[
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('posted', 'Posted'),
        ('rejected', 'Rejected'),
        ('rework', 'Rework'),
        ('scheduled', 'Scheduled')
    ], default='draft')

    
    def __str__(self):
        return f"Post by {self.creator.username} on {self.scheduled_date} - {self.status}"
