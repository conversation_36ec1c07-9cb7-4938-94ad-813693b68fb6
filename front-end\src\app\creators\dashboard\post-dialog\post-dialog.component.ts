import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';

export interface PostDialogData {
  date: string;
  projectId?: number;
  assignedProjects?: any[];
}

@Component({
  selector: 'app-post-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatIconModule
  ],
  templateUrl: './post-dialog.component.html'
})
export class PostDialogComponent implements OnInit {
  postForm: FormGroup;
  file: File | null = null;
  previewUrl: string | null = null;
  assignedProjects: any[] = [];
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private postService: PostService,
    private creatorDashboardService: CreatorDashboardService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<PostDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PostDialogData
  ) {
    // Parse the date from data.date (format: YYYY-MM-DD)
    const selectedDate = data.date ? new Date(data.date) : new Date();
    const defaultTime = '10:00'; // Default to 10:00 AM

    this.postForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      project: [data.projectId || '', Validators.required],
      scheduled_date: [selectedDate, Validators.required],
      scheduled_time: [defaultTime, Validators.required]
    });
  }

  ngOnInit(): void {
    // Load assigned projects if not provided
    if (this.data.assignedProjects) {
      this.assignedProjects = this.data.assignedProjects;
    } else {
      this.loadAssignedProjects();
    }
  }

  private loadAssignedProjects(): void {
    this.creatorDashboardService.getAssignedProjects().subscribe({
      next: (projects) => {
        this.assignedProjects = projects;
        if (projects.length > 0 && !this.postForm.get('project')?.value) {
          this.postForm.patchValue({ project: projects[0].id });
        }
      },
      error: (error) => {
        console.error('Error loading assigned projects:', error);
        this.snackBar.open('Error loading projects', 'Close', { duration: 3000 });
      }
    });
  }

  onFileChange(event: any): void {
    const selected = event.target.files[0];
    if (selected) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'];
      if (!allowedTypes.includes(selected.type)) {
        this.snackBar.open('Please select a valid image or video file', 'Close', { duration: 3000 });
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (selected.size > maxSize) {
        this.snackBar.open('File size must be less than 10MB', 'Close', { duration: 3000 });
        return;
      }

      this.file = selected;

      // Generate preview
      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result as string;
      };
      reader.readAsDataURL(selected);
    }
  }

  onSubmit(): void {
    if (this.postForm.invalid) {
      this.markFormGroupTouched();
      this.snackBar.open('Please fill out all required fields', 'Close', { duration: 3000 });
      return;
    }

    if (!this.file) {
      this.snackBar.open('Please select a media file', 'Close', { duration: 3000 });
      return;
    }

    this.isSubmitting = true;
    const formValue = this.postForm.value;

    // Combine date and time into a datetime string
    const scheduledDate = new Date(formValue.scheduled_date);
    const [hours, minutes] = formValue.scheduled_time.split(':');
    scheduledDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    // Format as ISO string for backend
    const scheduledDateTime = scheduledDate.toISOString().slice(0, 19); // Remove milliseconds and Z

    const formData = new FormData();
    formData.append('title', formValue.title);
    formData.append('description', formValue.description);
    formData.append('project', formValue.project.toString());
    formData.append('scheduled_time', scheduledDateTime);
    formData.append('media', this.file);

    console.log('Submitting post data:', {
      title: formValue.title,
      description: formValue.description,
      project: formValue.project,
      scheduled_time: scheduledDateTime,
      media: this.file.name
    });

    this.creatorDashboardService.createPost(formData).subscribe({
      next: (response) => {
        console.log('Post created successfully:', response);
        this.snackBar.open('Post created successfully!', 'Close', { duration: 3000 });
        this.dialogRef.close(response);
      },
      error: (error) => {
        console.error('Failed to create post:', error);
        let errorMessage = 'Failed to create post';
        if (error.error && error.error.error) {
          errorMessage += ': ' + error.error.error;
        } else if (error.error && typeof error.error === 'string') {
          errorMessage += ': ' + error.error;
        }
        this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        this.isSubmitting = false;
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.postForm.controls).forEach(key => {
      this.postForm.get(key)?.markAsTouched();
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
