import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, map } from 'rxjs';
import { CalendarEvent, CalendarProject, CreatorDashboardData } from '../dashboard/calendar-view/calendar-view.models';
import { Post } from '../models/post.model';

@Injectable({
  providedIn: 'root'
})
export class CreatorDashboardService {
  private readonly API_BASE = 'http://127.0.0.1:8000/api/creator-dashboard';
  
  // State management
  private assignedProjectsSubject = new BehaviorSubject<CalendarProject[]>([]);
  private calendarEventsSubject = new BehaviorSubject<CalendarEvent[]>([]);
  
  public assignedProjects$ = this.assignedProjectsSubject.asObservable();
  public calendarEvents$ = this.calendarEventsSubject.asObservable();

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  private getAuthHeadersForFormData(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData, let browser set it with boundary
    });
  }

  /**
   * Get projects assigned to the logged-in creator
   */
  getAssignedProjects(): Observable<CalendarProject[]> {
    return this.http.get<CalendarProject[]>(
      `${this.API_BASE}/my_projects/`,
      { headers: this.getAuthHeaders() }
    ).pipe(
      map(projects => {
        this.assignedProjectsSubject.next(projects);
        return projects;
      })
    );
  }

  /**
   * Get posts created by the creator (filtered by assigned projects)
   */
  getMyPosts(): Observable<Post[]> {
    return this.http.get<Post[]>(
      `${this.API_BASE}/my_posts/`,
      { headers: this.getAuthHeaders() }
    );
  }

  /**
   * Get calendar data with project assignments and thumbnails
   */
  getCalendarData(): Observable<CreatorDashboardData> {
    return this.http.get<CreatorDashboardData>(
      `${this.API_BASE}/calendar_data/`,
      { headers: this.getAuthHeaders() }
    ).pipe(
      map(data => {
        this.calendarEventsSubject.next(data.events);
        this.assignedProjectsSubject.next(data.projects);
        return data;
      })
    );
  }

  /**
   * Create a new post
   */
  createPost(postData: FormData): Observable<Post> {
    return this.http.post<Post>(
      `${this.API_BASE}/upload_post/`,
      postData,
      { headers: this.getAuthHeadersForFormData() }
    );
  }

  /**
   * Get posts for a specific date
   */
  getPostsForDate(date: Date): Observable<CalendarEvent[]> {
    const dateStr = date.toISOString().split('T')[0];
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => 
        event.date.startsWith(dateStr) && event.type === 'post'
      ))
    );
  }

  /**
   * Get projects for a specific date (deadlines)
   */
  getProjectsForDate(date: Date): Observable<CalendarEvent[]> {
    const dateStr = date.toISOString().split('T')[0];
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => 
        event.deadline?.startsWith(dateStr) && event.type === 'project'
      ))
    );
  }

  /**
   * Check if creator has access to a specific project
   */
  hasProjectAccess(projectId: number): Observable<boolean> {
    return this.assignedProjects$.pipe(
      map(projects => projects.some(project => project.id === projectId))
    );
  }

  /**
   * Get project details by ID (only if assigned)
   */
  getProjectById(projectId: number): Observable<CalendarProject | null> {
    return this.assignedProjects$.pipe(
      map(projects => projects.find(project => project.id === projectId) || null)
    );
  }

  /**
   * Filter events by project
   */
  filterEventsByProject(projectId: number): Observable<CalendarEvent[]> {
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => event.projectId === projectId))
    );
  }

  /**
   * Filter events by company
   */
  filterEventsByCompany(companyId: number): Observable<CalendarEvent[]> {
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => event.companyId === companyId))
    );
  }

  /**
   * Get events for calendar view (posts and project deadlines)
   */
  getEventsForCalendar(): Observable<CalendarEvent[]> {
    return this.calendarEvents$;
  }

  /**
   * Refresh all dashboard data
   */
  refreshDashboardData(): Observable<CreatorDashboardData> {
    return this.getCalendarData();
  }

  /**
   * Clear cached data (for logout)
   */
  clearCache(): void {
    this.assignedProjectsSubject.next([]);
    this.calendarEventsSubject.next([]);
  }
}
