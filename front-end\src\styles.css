/* You can add global styles to this file, and also import other style files */

/* Angular Material Theme */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Global Styles */
html, body {
  height: 100%;
  margin: 0;
  font-family: 'Roboto', sans-serif;
}

/* Material Icons Fix */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Ensure mat-icon renders properly */
mat-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  min-width: 24px;
  min-height: 24px;
}
